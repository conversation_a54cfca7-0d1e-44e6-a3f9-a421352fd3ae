import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { DollarSign, Clock, CheckCircle, ArrowUpRight } from "lucide-react";
import Link from 'next/link';
import { mockProjects } from "@/lib/mock-data";


export default function FreelancerDashboard() {
  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="text-lg font-semibold md:text-2xl">Dashboard</h1>
         <Button asChild variant="outline">
            <Link href="/freelancer/projects">Find Projects</Link>
        </Button>
      </div>
      <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$45,231.89</div>
            <p className="text-xs text-muted-foreground">+20.1% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+1</div>
            <p className="text-xs text-muted-foreground">You have 3 proposals pending</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profile Completion</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">85%</div>
            <Progress value={85} className="mt-2" />
          </CardContent>
        </Card>
      </div>
      <div className="grid gap-4 md:gap-8 lg:grid-cols-2 xl:grid-cols-3">
        <Card className="xl:col-span-2">
            <CardHeader>
                <CardTitle>Recent Proposals</CardTitle>
                <CardDescription>An overview of your recent project applications.</CardDescription>
            </CardHeader>
            <CardContent>
                {mockProjects.slice(0, 4).map(project => (
                    <div key={project.id} className="flex items-center justify-between py-3 border-b last:border-0">
                        <div>
                            <p className="font-medium">{project.title}</p>
                            <p className="text-sm text-muted-foreground">Client: {project.clientName}</p>
                        </div>
                        <span className="text-sm capitalize text-muted-foreground">Submitted</span>
                    </div>
                ))}
            </CardContent>
            <CardFooter>
                 <Button asChild size="sm" variant="outline">
                    <Link href="#">View All Proposals <ArrowUpRight className="h-4 w-4 ml-2"/></Link>
                 </Button>
            </CardFooter>
        </Card>
        <Card>
            <CardHeader>
                <CardTitle>Recommended Projects</CardTitle>
                <CardDescription>AI-powered suggestions based on your skills.</CardDescription>
            </CardHeader>
             <CardContent>
                 <p className="text-sm text-muted-foreground">Feature coming soon.</p>
            </CardContent>
        </Card>
      </div>
    </>
  );
}
