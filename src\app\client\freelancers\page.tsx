import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { mockFreelancers } from "@/lib/mock-data";
import { CheckCircle, Star } from "lucide-react";
import Link from "next/link";

export default function BrowseFreelancersPage() {
  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="text-lg font-semibold md:text-2xl">Find Freelancers</h1>
      </div>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {mockFreelancers.map((freelancer) => (
          <Card key={freelancer.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <CardHeader className="p-0">
                <div className="h-24 bg-primary/20" />
            </CardHeader>
            <CardContent className="p-6 pt-0 text-center">
                <Avatar className="w-24 h-24 mx-auto -mt-12 border-4 border-background">
                    <AvatarImage src={freelancer.avatarUrl} alt={freelancer.name} data-ai-hint="person" />
                    <AvatarFallback>{freelancer.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <Link href="#" className="text-lg font-semibold mt-4 block hover:text-primary">{freelancer.name}</Link>
                <div className="flex items-center justify-center text-muted-foreground mt-1">
                    <p className="text-sm">{freelancer.title}</p>
                    {freelancer.isVerified && <CheckCircle className="w-4 h-4 ml-1 text-blue-500" />}
                </div>

                 <div className="flex items-center justify-center gap-1 mt-2">
                    <Star className="w-4 h-4 text-amber-400 fill-amber-400" />
                    <span className="font-semibold">{freelancer.rating.toFixed(1)}</span>
                    <span className="text-sm text-muted-foreground">/ 5.0</span>
                </div>
                
                <div className="mt-4 flex flex-wrap justify-center gap-2">
                    {freelancer.skills.slice(0, 4).map(skill => (
                        <Badge key={skill} variant="secondary">{skill}</Badge>
                    ))}
                </div>

                <div className="mt-6">
                    <p className="text-2xl font-bold">${freelancer.hourlyRate}<span className="text-sm font-normal text-muted-foreground">/hr</span></p>
                </div>
               
                <Button className="mt-4 w-full">View Profile</Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
}
