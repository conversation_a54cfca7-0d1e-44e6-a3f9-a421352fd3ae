'use client';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useEntranceAnimation, useParallax } from '@/hooks/useGSAP';
import { Button as AntButton } from 'antd';
import { PlayCircleOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { HeroBackground } from '@/components/shared/BackgroundElements';
import '@/styles/hero.css';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}



export function Hero() {
  const heroRef = useRef<HTMLElement>(null);
  const titleRef = useEntranceAnimation('textReveal', { delay: 0.5 });
  const subtitleRef = useEntranceAnimation('fadeIn', { delay: 1 });
  const buttonsRef = useEntranceAnimation('slideIn', { direction: 'up', delay: 1.5 });
  const backgroundRef = useParallax(0.3);

  useEffect(() => {
    if (!heroRef.current) return;

    // Create a timeline for complex animations
    const tl = gsap.timeline();

    // Animate background elements
    tl.fromTo('.hero-bg-element',
      { scale: 1.1, opacity: 0 },
      { scale: 1, opacity: 1, duration: 2, ease: 'power2.out' }
    )
    .fromTo('.hero-content-wrapper',
      { y: 50, opacity: 0 },
      { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out' },
      '-=1.5'
    );

    // Parallax effect for background
    gsap.to('.hero-parallax', {
      yPercent: -50,
      ease: 'none',
      scrollTrigger: {
        trigger: heroRef.current,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true,
      },
    });

    return () => {
      tl.kill();
    };
  }, []);

  return (
    <section
      ref={heroRef}
      className="relative w-full h-screen flex items-center justify-center overflow-hidden"
    >
      {/* Enhanced background with all elements */}
      <HeroBackground />

      {/* Background image with parallax */}
      <div className="absolute inset-0 hero-bg-element">
        <div
          ref={backgroundRef}
          className="hero-parallax absolute inset-0 scale-110"
        >
          <Image
            src="https://placehold.co/1920x1080.png"
            alt="Background"
            fill
            className="object-cover opacity-30"
            data-ai-hint="collaboration teamwork"
            priority
          />
        </div>
      </div>

      {/* Glass morphism overlay */}
      <div className="absolute inset-0 z-10 bg-gradient-to-b from-black/20 via-transparent to-black/40 backdrop-blur-[1px]" />

      {/* Main content */}
      <div className="relative z-20 container mx-auto px-4 md:px-6 text-center text-white hero-content-wrapper">
        <div className="max-w-5xl mx-auto">
          {/* Main title with enhanced styling */}
          <div className="relative">
            <h1
              ref={titleRef}
              className="text-5xl md:text-7xl lg:text-8xl font-bold font-headline tracking-tight drop-shadow-2xl leading-tight hero-title-gradient"
              data-animate="true"
              aria-label="VERTEX FORGE - Professional Networking Platform"
            >
              VERTEX FORGE
            </h1>
            <div className="absolute -inset-4 bg-gradient-to-r from-amber-600/20 to-amber-800/20 blur-3xl -z-10" />
          </div>

          {/* Subtitle */}
          <p
            ref={subtitleRef}
            className="mt-8 max-w-3xl mx-auto text-xl md:text-2xl lg:text-3xl text-gray-200 drop-shadow-lg font-light leading-relaxed"
          >
            Where Great Projects Meet Great Talent
          </p>

          <p className="mt-4 max-w-2xl mx-auto text-lg md:text-xl text-gray-300/90 drop-shadow-md">
            The premier marketplace connecting innovative clients with expert freelance professionals.
          </p>

          {/* Action buttons */}
          <div
            ref={buttonsRef}
            className="mt-12 flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <Link href="/client/freelancers">
              <AntButton
                type="primary"
                size="large"
                icon={<ArrowRightOutlined />}
                className="h-14 px-8 text-lg font-semibold bg-gradient-to-r from-amber-600 to-amber-700 border-none hover:from-amber-500 hover:to-amber-600 shadow-2xl hover:shadow-amber-500/25 transition-all duration-300 hero-button-primary btn-hover-lift"
              >
                Find Talent
              </AntButton>
            </Link>

            <Link href="/freelancer/projects">
              <AntButton
                size="large"
                icon={<PlayCircleOutlined />}
                className="h-14 px-8 text-lg font-semibold bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm shadow-2xl transition-all duration-300 hero-button-secondary glass-morphism"
              >
                Find Work
              </AntButton>
            </Link>
          </div>

          {/* Scroll indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
              <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-scroll-bounce" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
