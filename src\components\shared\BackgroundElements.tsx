'use client';

import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import '@/styles/hero.css';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface BackgroundElementsProps {
  variant?: 'hero' | 'features' | 'testimonials' | 'default';
  density?: 'low' | 'medium' | 'high';
  animated?: boolean;
}

export function BackgroundElements({ 
  variant = 'default', 
  density = 'medium',
  animated = true 
}: BackgroundElementsProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current || !animated) return;

    const container = containerRef.current;

    // Animate floating shapes
    gsap.to('.floating-shape', {
      y: 'random(-20, 20)',
      x: 'random(-15, 15)',
      rotation: 'random(-180, 180)',
      duration: 'random(8, 12)',
      ease: 'sine.inOut',
      repeat: -1,
      yoyo: true,
      stagger: {
        amount: 2,
        from: 'random'
      }
    });

    // Animate morphing blobs
    gsap.to('.morph-blob-1', {
      scale: 'random(0.8, 1.2)',
      duration: 'random(15, 20)',
      ease: 'sine.inOut',
      repeat: -1,
      yoyo: true
    });

    gsap.to('.morph-blob-2', {
      scale: 'random(0.9, 1.1)',
      duration: 'random(18, 25)',
      ease: 'sine.inOut',
      repeat: -1,
      yoyo: true
    });

    // Parallax effect for background elements
    gsap.to('.parallax-slow', {
      yPercent: -30,
      ease: 'none',
      scrollTrigger: {
        trigger: container,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true,
      },
    });

    gsap.to('.parallax-medium', {
      yPercent: -50,
      ease: 'none',
      scrollTrigger: {
        trigger: container,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true,
      },
    });

    gsap.to('.parallax-fast', {
      yPercent: -70,
      ease: 'none',
      scrollTrigger: {
        trigger: container,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true,
      },
    });

    // Cleanup
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, [animated]);

  const getShapeCount = () => {
    switch (density) {
      case 'low': return 3;
      case 'medium': return 6;
      case 'high': return 10;
      default: return 6;
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'hero':
        return 'from-black via-gray-900 to-amber-900/20';
      case 'features':
        return 'from-black via-gray-900 to-black';
      case 'testimonials':
        return 'from-black via-gray-900 to-black';
      default:
        return 'from-black via-gray-900 to-black';
    }
  };

  return (
    <div 
      ref={containerRef}
      className="absolute inset-0 overflow-hidden pointer-events-none"
      style={{ zIndex: 1 }}
    >
      {/* Base gradient background */}
      <div className={`absolute inset-0 bg-gradient-to-b ${getVariantStyles()}`} />

      {/* Texture overlays */}
      <div className="absolute inset-0 texture-noise opacity-30" />
      <div className="absolute inset-0 texture-grain opacity-20" />

      {/* Morphing background blobs */}
      <div className="absolute top-20 right-20 w-96 h-96 morph-blob-1 parallax-slow">
        <div className="w-full h-full bg-gradient-to-br from-amber-600/10 to-amber-800/5 blur-3xl" />
      </div>
      
      <div className="absolute bottom-32 left-32 w-80 h-80 morph-blob-2 parallax-medium">
        <div className="w-full h-full bg-gradient-to-br from-amber-700/8 to-amber-900/4 blur-2xl" />
      </div>

      {/* Floating geometric shapes */}
      {Array.from({ length: getShapeCount() }).map((_, index) => {
        const shapes = ['circle', 'square', 'triangle-up', 'triangle-down', 'hexagon'];
        const shape = shapes[index % shapes.length];
        const size = Math.random() * 40 + 20;
        const top = Math.random() * 80 + 10;
        const left = Math.random() * 80 + 10;
        const parallaxClass = index % 3 === 0 ? 'parallax-fast' : index % 2 === 0 ? 'parallax-medium' : 'parallax-slow';

        return (
          <div
            key={index}
            className={`floating-shape floating-${shape} ${parallaxClass}`}
            style={{
              top: `${top}%`,
              left: `${left}%`,
              width: shape === 'circle' || shape === 'square' ? `${size}px` : undefined,
              height: shape === 'circle' || shape === 'square' ? `${size}px` : undefined,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        );
      })}

      {/* Particle effects */}
      {density === 'high' && Array.from({ length: 15 }).map((_, index) => (
        <div
          key={`particle-${index}`}
          className="particle parallax-fast"
          style={{
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 8}s`,
          }}
        />
      ))}

      {/* Gradient overlays */}
      <div className="absolute inset-0 gradient-overlay-radial" />
      <div className="absolute inset-0 gradient-overlay-linear" />

      {/* Additional texture patterns based on variant */}
      {variant === 'hero' && (
        <>
          <div className="absolute inset-0 texture-dots opacity-10" />
          <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-black/50 to-transparent" />
        </>
      )}

      {variant === 'features' && (
        <div className="absolute inset-0 texture-grid opacity-5" />
      )}

      {variant === 'testimonials' && (
        <div className="absolute inset-0 texture-lines opacity-8" />
      )}
    </div>
  );
}

// Utility component for section-specific background elements
export function HeroBackground() {
  return <BackgroundElements variant="hero" density="high" animated />;
}

export function FeaturesBackground() {
  return <BackgroundElements variant="features" density="medium" animated />;
}

export function TestimonialsBackground() {
  return <BackgroundElements variant="testimonials" density="medium" animated />;
}

export function DefaultBackground() {
  return <BackgroundElements variant="default" density="low" animated />;
}
