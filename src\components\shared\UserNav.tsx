'use client';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CreditCard, LayoutDashboard, LogOut, Settings, User } from "lucide-react";
import Link from "next/link";

type UserNavProps = {
    role: 'client' | 'freelancer';
}

export function UserNav({ role }: UserNavProps) {
  const profileLink = `/${role}/profile/edit`;
  const dashboardLink = `/${role}/dashboard`;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-10 w-10 rounded-full">
          <Avatar className="h-10 w-10">
            <AvatarImage src="https://placehold.co/100x100.png" alt="User avatar" data-ai-hint="person" />
            <AvatarFallback>U</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">John Doe</p>
            <p className="text-xs leading-none text-muted-foreground">
              <EMAIL>
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href={dashboardLink}><LayoutDashboard className="mr-2 h-4 w-4" /><span>DASHBOARD</span></Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
             <Link href={profileLink}><User className="mr-2 h-4 w-4" /><span>PROFILE</span></Link>
          </DropdownMenuItem>
          {role === 'client' && (
            <DropdownMenuItem asChild>
              <Link href="/client/payments"><CreditCard className="mr-2 h-4 w-4" /><span>BILLING</span></Link>
            </DropdownMenuItem>
          )}
           <DropdownMenuItem disabled>
            <Settings className="mr-2 h-4 w-4" />
            <span>SETTINGS</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
           <Link href="/"><LogOut className="mr-2 h-4 w-4" /><span>LOG OUT</span></Link>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
