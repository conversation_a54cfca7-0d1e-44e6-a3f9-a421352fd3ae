@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 47 24% 96%;
    --foreground: 215 28% 17%;
    --card: 0 0% 100%;
    --card-foreground: 215 28% 17%;
    --popover: 0 0% 100%;
    --popover-foreground: 215 28% 17%;
    --primary: 207 26% 56%;
    --primary-foreground: 210 20% 98%;
    --secondary: 207 26% 90%;
    --secondary-foreground: 215 28% 17%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 4 85% 67%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 207 26% 85%;
    --input: 207 26% 88%;
    --ring: 207 26% 56%;
    --chart-1: 207 30% 50%;
    --chart-2: 4 80% 60%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 215 28% 17%;
    --foreground: 210 20% 98%;
    --card: 215 28% 13%;
    --card-foreground: 210 20% 98%;
    --popover: 215 28% 13%;
    --popover-foreground: 210 20% 98%;
    --primary: 207 26% 56%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 28% 25%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 28% 25%;
    --muted-foreground: 217 10% 65%;
    --accent: 4 85% 67%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 215 28% 25%;
    --input: 215 28% 25%;
    --ring: 207 26% 56%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-headline;
  }
  p, div, span, a, li, button, input, textarea, select {
    @apply font-body;
  }
}

/* Enhanced smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Focus styles for keyboard navigation */
.keyboard-navigation *:focus {
  outline: 2px solid #d4af37;
  outline-offset: 2px;
}

/* Enhanced button hover effects */
.btn-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}

/* Loading animation for images */
.image-loading {
  background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced glass morphism */
.glass-enhanced {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Scroll indicator */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #d4af37, #b8860b);
  transform-origin: left;
  z-index: 9999;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #d4af37, #b8860b);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #b8860b, #d4af37);
}
