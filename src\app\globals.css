@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 47 24% 96%;
    --foreground: 215 28% 17%;
    --card: 0 0% 100%;
    --card-foreground: 215 28% 17%;
    --popover: 0 0% 100%;
    --popover-foreground: 215 28% 17%;
    --primary: 207 26% 56%;
    --primary-foreground: 210 20% 98%;
    --secondary: 207 26% 90%;
    --secondary-foreground: 215 28% 17%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 4 85% 67%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 207 26% 85%;
    --input: 207 26% 88%;
    --ring: 207 26% 56%;
    --chart-1: 207 30% 50%;
    --chart-2: 4 80% 60%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 215 28% 17%;
    --foreground: 210 20% 98%;
    --card: 215 28% 13%;
    --card-foreground: 210 20% 98%;
    --popover: 215 28% 13%;
    --popover-foreground: 210 20% 98%;
    --primary: 207 26% 56%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 28% 25%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 28% 25%;
    --muted-foreground: 217 10% 65%;
    --accent: 4 85% 67%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 215 28% 25%;
    --input: 215 28% 25%;
    --ring: 207 26% 56%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-headline;
  }
  p, div, span, a, li, button, input, textarea, select {
    @apply font-body;
  }
}
