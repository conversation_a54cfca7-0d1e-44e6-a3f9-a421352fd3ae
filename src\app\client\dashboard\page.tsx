import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  Card<PERSON><PERSON>le,
  CardFooter,
} from "@/components/ui/card";
import { mockProjects } from "@/lib/mock-data";
import { CheckCircle, Clock, PlusCircle, ArrowUpRight } from "lucide-react";
import Link from 'next/link';

export default function ClientDashboard() {
  const activeProjects = mockProjects.filter(p => p.status === 'in-progress');
  const openProjects = mockProjects.filter(p => p.status === 'open');

  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="text-lg font-semibold md:text-2xl">Dashboard</h1>
        <Button asChild>
            <Link href="/client/projects/new"><PlusCircle className="h-4 w-4 mr-2"/> Post New Project</Link>
        </Button>
      </div>
      <div
        className="flex flex-1 rounded-lg"
      >
        <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-3 w-full">
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                    <span className="text-muted-foreground">$</span>
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">$15,231.89</div>
                    <p className="text-xs text-muted-foreground">+20.1% from last month</p>
                </CardContent>
            </Card>
             <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">{activeProjects.length}</div>
                    <p className="text-xs text-muted-foreground">{openProjects.length} projects pending</p>
                </CardContent>
            </Card>
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Hires</CardTitle>
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">+12</div>
                    <p className="text-xs text-muted-foreground">Completed projects this year</p>
                </CardContent>
            </Card>
        </div>
      </div>
      <div className="grid gap-4 md:gap-8 lg:grid-cols-2 xl:grid-cols-3">
        <Card className="xl:col-span-2">
            <CardHeader>
                <CardTitle>Recent Projects</CardTitle>
                <CardDescription>An overview of your recently posted or active projects.</CardDescription>
            </CardHeader>
            <CardContent>
                {mockProjects.slice(0, 4).map(project => (
                    <div key={project.id} className="flex items-center justify-between py-3 border-b last:border-0">
                        <div>
                            <p className="font-medium">{project.title}</p>
                            <p className="text-sm text-muted-foreground">Budget: ${project.budget}</p>
                        </div>
                        <span className="text-sm capitalize text-muted-foreground">{project.status}</span>
                    </div>
                ))}
            </CardContent>
            <CardFooter>
                 <Button asChild size="sm" variant="outline">
                    <Link href="#">View All Projects <ArrowUpRight className="h-4 w-4 ml-2"/></Link>
                 </Button>
            </CardFooter>
        </Card>
        <Card>
            <CardHeader>
                <CardTitle>Recommended Freelancers</CardTitle>
                <CardDescription>AI-powered suggestions based on your project history.</CardDescription>
            </CardHeader>
             <CardContent>
                 <p className="text-sm text-muted-foreground">Feature coming soon.</p>
            </CardContent>
        </Card>
      </div>
    </>
  );
}
