'use server';
/**
 * @fileOverview An AI agent that suggests relevant matches between freelancers and projects.
 *
 * - suggestMatches - A function that suggests relevant matches.
 * - SuggestMatchesInput - The input type for the suggestMatches function.
 * - SuggestMatchesOutput - The return type for the suggestMatches function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SuggestMatchesInputSchema = z.object({
  userType: z.enum(['client', 'freelancer']).describe('The type of user making the request.'),
  query: z.string().describe('The user query or description of their needs.'),
  profile: z.string().optional().describe('The user profile information.'),
});
export type SuggestMatchesInput = z.infer<typeof SuggestMatchesInputSchema>;

const SuggestMatchesOutputSchema = z.object({
  suggestions: z.array(z.string()).describe('A list of suggestions for relevant matches.'),
});
export type SuggestMatchesOutput = z.infer<typeof SuggestMatchesOutputSchema>;

export async function suggestMatches(input: SuggestMatchesInput): Promise<SuggestMatchesOutput> {
  return suggestMatchesFlow(input);
}

const prompt = ai.definePrompt({
  name: 'suggestMatchesPrompt',
  input: {schema: SuggestMatchesInputSchema},
  output: {schema: SuggestMatchesOutputSchema},
  prompt: `You are a matching expert, suggesting relevant matches between freelancers and projects.

You will receive a user type (client or freelancer), a query describing their needs, and optionally their profile information.
Based on this information, you will provide a list of suggestions for relevant matches.

User Type: {{{userType}}}
Query: {{{query}}}
Profile: {{{profile}}}

Suggestions:`,
});

const suggestMatchesFlow = ai.defineFlow(
  {
    name: 'suggestMatchesFlow',
    inputSchema: SuggestMatchesInputSchema,
    outputSchema: SuggestMatchesOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
