/* Hero section custom styles */

.hero-shape-1 {
  transform: rotate(45deg);
}

.hero-shape-2 {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.hero-texture-grain {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  background-size: 256px 256px;
}

.hero-title-gradient {
  background: linear-gradient(135deg, #ffffff 0%, #d4d4d4 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-button-primary {
  border-radius: 12px !important;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hero-button-secondary {
  border-radius: 12px !important;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Radial gradient utility */
.bg-radial-gradient {
  background: radial-gradient(circle at center, var(--tw-gradient-stops));
}

/* Custom animations */
@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-medium {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes float-strong {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float-gentle {
  animation: float-gentle 3s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 4s ease-in-out infinite;
}

.animate-float-strong {
  animation: float-strong 3.5s ease-in-out infinite;
}

/* Scroll indicator animation */
@keyframes scroll-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.animate-scroll-bounce {
  animation: scroll-bounce 2s infinite;
}

/* Glass morphism effects */
.glass-morphism {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-morphism-dark {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Texture overlays */
.texture-grain {
  position: relative;
}

.texture-grain::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  background-size: 256px 256px;
  opacity: 0.3;
  pointer-events: none;
}

/* Gradient text utilities */
.text-gradient-gold {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-white {
  background: linear-gradient(135deg, #ffffff 0%, #d4d4d4 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button hover effects */
.btn-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(139, 69, 19, 0.3);
}

/* Advanced Background Elements */

/* Floating geometric shapes */
.floating-shape {
  position: absolute;
  pointer-events: none;
  z-index: 1;
}

.floating-circle {
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(184, 134, 11, 0.05));
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}

.floating-triangle {
  width: 0;
  height: 0;
  border-style: solid;
}

.floating-triangle-up {
  border-left: 25px solid transparent;
  border-right: 25px solid transparent;
  border-bottom: 43px solid rgba(212, 175, 55, 0.1);
}

.floating-triangle-down {
  border-left: 25px solid transparent;
  border-right: 25px solid transparent;
  border-top: 43px solid rgba(212, 175, 55, 0.1);
}

.floating-square {
  background: linear-gradient(45deg, rgba(212, 175, 55, 0.08), rgba(184, 134, 11, 0.04));
  -webkit-backdrop-filter: blur(1px);
  backdrop-filter: blur(1px);
  transform: rotate(45deg);
}

.floating-hexagon {
  width: 60px;
  height: 34.64px;
  background: rgba(212, 175, 55, 0.1);
  position: relative;
}

.floating-hexagon:before,
.floating-hexagon:after {
  content: "";
  position: absolute;
  width: 0;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
}

.floating-hexagon:before {
  bottom: 100%;
  border-bottom: 17.32px solid rgba(212, 175, 55, 0.1);
}

.floating-hexagon:after {
  top: 100%;
  border-top: 17.32px solid rgba(212, 175, 55, 0.1);
}

/* Morphing background elements */
@keyframes morph-blob-1 {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    transform: translate(30px, -50px) rotate(90deg);
  }
  50% {
    border-radius: 50% 60% 30% 60% / 30% 60% 70% 40%;
    transform: translate(-20px, 20px) rotate(180deg);
  }
  75% {
    border-radius: 60% 40% 60% 30% / 60% 30% 60% 70%;
    transform: translate(-30px, -30px) rotate(270deg);
  }
}

@keyframes morph-blob-2 {
  0%, 100% {
    border-radius: 40% 60% 70% 30% / 40% 70% 30% 60%;
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    border-radius: 70% 30% 40% 60% / 60% 40% 60% 30%;
    transform: translate(-40px, 40px) rotate(120deg);
  }
  66% {
    border-radius: 30% 70% 60% 40% / 70% 30% 40% 60%;
    transform: translate(40px, -20px) rotate(240deg);
  }
}

.morph-blob-1 {
  animation: morph-blob-1 20s ease-in-out infinite;
}

.morph-blob-2 {
  animation: morph-blob-2 25s ease-in-out infinite reverse;
}

/* Advanced texture overlays */
.texture-noise {
  background-image:
    radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(184, 134, 11, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.02) 0%, transparent 50%),
    url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  background-size: 100% 100%, 100% 100%, 100% 100%, 256px 256px;
}

.texture-dots {
  background-image: radial-gradient(circle, rgba(212, 175, 55, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.texture-lines {
  background-image:
    linear-gradient(90deg, rgba(212, 175, 55, 0.05) 1px, transparent 1px),
    linear-gradient(rgba(212, 175, 55, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.texture-grid {
  background-image:
    linear-gradient(rgba(212, 175, 55, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(212, 175, 55, 0.03) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* Particle effects */
@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-40px) translateX(-5px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-20px) translateX(-15px) rotate(270deg);
    opacity: 0.4;
  }
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(212, 175, 55, 0.6);
  border-radius: 50%;
  animation: particle-float 8s ease-in-out infinite;
  pointer-events: none;
}

.particle:nth-child(2n) {
  animation-delay: -2s;
  animation-duration: 10s;
}

.particle:nth-child(3n) {
  animation-delay: -4s;
  animation-duration: 12s;
}

.particle:nth-child(4n) {
  animation-delay: -6s;
  animation-duration: 9s;
}

/* Gradient overlays */
.gradient-overlay-radial {
  background: radial-gradient(
    ellipse at center,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.3) 100%
  );
}

.gradient-overlay-linear {
  background: linear-gradient(
    135deg,
    rgba(212, 175, 55, 0.05) 0%,
    rgba(0, 0, 0, 0) 50%,
    rgba(184, 134, 11, 0.05) 100%
  );
}

/* Advanced glass morphism */
.glass-card {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-card-dark {
  background: rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Responsive utilities */
@media (max-width: 480px) {
  .hero-title-gradient {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .floating-shape,
  .hero-shape-1,
  .hero-shape-2 {
    display: none;
  }

  .morph-blob-1,
  .morph-blob-2 {
    animation-duration: 10s;
    transform: scale(0.5);
  }

  .glass-card,
  .glass-card-dark {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }
}

@media (max-width: 768px) {
  .hero-title-gradient {
    font-size: 3rem;
    line-height: 1.1;
  }

  .floating-shape {
    transform: scale(0.7);
  }

  .morph-blob-1,
  .morph-blob-2 {
    animation-duration: 15s;
    transform: scale(0.8);
  }

  .particle {
    display: none;
  }
}

@media (max-width: 1024px) {
  .morph-blob-1,
  .morph-blob-2 {
    transform: scale(0.9);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-card,
  .glass-card-dark {
    background: rgba(255, 255, 255, 0.9);
    color: #000;
    border: 2px solid #000;
  }

  .text-gradient-gold,
  .text-gradient-white,
  .hero-title-gradient {
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    color: #000;
  }

  .floating-shape,
  .morph-blob-1,
  .morph-blob-2 {
    display: none;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .floating-shape,
  .particle,
  .morph-blob-1,
  .morph-blob-2 {
    animation: none;
    transform: none;
  }

  .animate-float-gentle,
  .animate-float-medium,
  .animate-float-strong,
  .animate-scroll-bounce {
    animation: none;
  }
}

/* Print styles */
@media print {
  .floating-shape,
  .particle,
  .morph-blob-1,
  .morph-blob-2,
  .glass-card,
  .glass-card-dark {
    display: none !important;
  }

  .text-gradient-gold,
  .text-gradient-white,
  .hero-title-gradient {
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: unset !important;
    background-clip: unset !important;
    color: #000 !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
