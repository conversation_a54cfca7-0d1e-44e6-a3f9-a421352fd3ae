import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

export default function NewProjectPage() {
  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="text-lg font-semibold md:text-2xl">Post a New Project</h1>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Project Details</CardTitle>
          <CardDescription>
            Fill out the form below to post your new project. Be as detailed as possible to attract the best talent.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="grid gap-6">
            <div className="grid gap-3">
              <Label htmlFor="title">Project Title</Label>
              <Input
                id="title"
                type="text"
                className="w-full"
                placeholder="e.g., E-commerce Website Redesign"
              />
            </div>
            <div className="grid gap-3">
              <Label htmlFor="description">Project Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your project in detail..."
                className="min-h-32"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="grid gap-3">
                    <Label htmlFor="skills">Required Skills</Label>
                    <Input id="skills" type="text" placeholder="e.g., React, Node.js, Figma" />
                    <p className="text-xs text-muted-foreground">Separate skills with a comma.</p>
                </div>
                <div className="grid gap-3">
                    <Label htmlFor="budget">Budget (USD)</Label>
                    <Input id="budget" type="number" placeholder="e.g., 5000" />
                </div>
            </div>
          </form>
        </CardContent>
        <CardFooter className="border-t px-6 py-4">
          <Button>Post Project</Button>
        </CardFooter>
      </Card>
    </>
  );
}
