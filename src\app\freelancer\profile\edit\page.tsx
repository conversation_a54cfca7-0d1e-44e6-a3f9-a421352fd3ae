import { AadharVerification } from "@/components/auth/AadharVerification";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

export default function EditProfilePage() {
  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="text-lg font-semibold md:text-2xl">Edit Profile</h1>
      </div>
      <form className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="grid gap-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input id="firstName" defaultValue="Alice" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input id="lastName" defaultValue="Johnson" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" defaultValue="<EMAIL>" disabled />
            </div>
             <div className="grid gap-2">
              <Label htmlFor="title">Professional Title</Label>
              <Input id="title" defaultValue="Senior UI/UX Designer" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Professional Details</CardTitle>
          </CardHeader>
           <CardContent className="grid gap-6">
            <div className="grid gap-2">
              <Label htmlFor="bio">Biography</Label>
              <Textarea id="bio" placeholder="Tell clients about yourself..." className="min-h-24" defaultValue="10+ years of experience in creating beautiful and user-friendly digital experiences. Passionate about clean design and great usability." />
            </div>
             <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="grid gap-2">
                    <Label htmlFor="skills">Skills</Label>
                    <Input id="skills" placeholder="e.g., Figma, Webflow" defaultValue="UI/UX Design, Figma, Webflow, Branding" />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="rate">Hourly Rate (USD)</Label>
                    <Input id="rate" type="number" placeholder="e.g., 95" defaultValue="95" />
                </div>
             </div>
          </CardContent>
        </Card>
        
        <Card>
            <CardHeader>
                <CardTitle>Verification</CardTitle>
            </CardHeader>
            <CardContent>
                 <AadharVerification />
            </CardContent>
        </Card>

        <Card>
            <CardHeader>
                <CardTitle>Portfolio</CardTitle>
                <CardDescription>Showcase your best work. Upload up to 5 images.</CardDescription>
            </CardHeader>
            <CardContent>
                <Input id="portfolio" type="file" multiple />
            </CardContent>
        </Card>
        
        <div className="flex justify-end">
            <Button type="submit">Save Changes</Button>
        </div>
      </form>
    </>
  );
}
