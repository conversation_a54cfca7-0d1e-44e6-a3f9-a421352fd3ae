'use client';

import React from 'react';
import { ConfigProvider } from 'antd';
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// Ant Design theme configuration
const antdTheme = {
  token: {
    // Primary colors following black/brown/white scheme
    colorPrimary: '#8B4513', // Saddle brown
    colorSuccess: '#D2691E', // Chocolate
    colorWarning: '#CD853F', // Peru
    colorError: '#A0522D', // Sienna
    colorInfo: '#8B4513', // Saddle brown
    
    // Background colors
    colorBgBase: '#000000', // Black
    colorBgContainer: '#1a1a1a', // Dark gray
    colorBgElevated: '#2a2a2a', // Lighter dark gray
    
    // Text colors
    colorText: '#ffffff', // White
    colorTextSecondary: '#d4d4d4', // Light gray
    colorTextTertiary: '#a3a3a3', // Medium gray
    
    // Border colors
    colorBorder: '#404040', // Dark border
    colorBorderSecondary: '#525252', // Lighter border
    
    // Font settings
    fontFamily: 'PT Sans, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: 14,
    fontSizeHeading1: 38,
    fontSizeHeading2: 30,
    fontSizeHeading3: 24,
    fontSizeHeading4: 20,
    fontSizeHeading5: 16,
    
    // Border radius for modern look
    borderRadius: 8,
    borderRadiusLG: 12,
    borderRadiusSM: 6,
    
    // Shadows for depth
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    boxShadowSecondary: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  },
  components: {
    Button: {
      colorPrimary: '#8B4513',
      algorithm: true,
    },
    Input: {
      colorBgContainer: '#1a1a1a',
      colorBorder: '#404040',
    },
    Card: {
      colorBgContainer: '#1a1a1a',
      colorBorderSecondary: '#404040',
    },
  },
};

// Material-UI theme configuration
const muiTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#8B4513', // Saddle brown
      light: '#CD853F', // Peru
      dark: '#654321', // Dark brown
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#D2691E', // Chocolate
      light: '#DEB887', // Burlywood
      dark: '#A0522D', // Sienna
      contrastText: '#ffffff',
    },
    background: {
      default: '#000000', // Black
      paper: '#1a1a1a', // Dark gray
    },
    text: {
      primary: '#ffffff', // White
      secondary: '#d4d4d4', // Light gray
    },
    divider: '#404040', // Dark border
  },
  typography: {
    fontFamily: 'PT Sans, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    h1: {
      fontFamily: 'Playfair Display, serif',
      fontWeight: 700,
      fontSize: '2.5rem',
      lineHeight: 1.2,
    },
    h2: {
      fontFamily: 'Playfair Display, serif',
      fontWeight: 700,
      fontSize: '2rem',
      lineHeight: 1.3,
    },
    h3: {
      fontFamily: 'Playfair Display, serif',
      fontWeight: 400,
      fontSize: '1.75rem',
      lineHeight: 1.4,
    },
    h4: {
      fontFamily: 'PT Sans, sans-serif',
      fontWeight: 700,
      fontSize: '1.5rem',
      lineHeight: 1.4,
    },
    h5: {
      fontFamily: 'PT Sans, sans-serif',
      fontWeight: 700,
      fontSize: '1.25rem',
      lineHeight: 1.5,
    },
    h6: {
      fontFamily: 'PT Sans, sans-serif',
      fontWeight: 700,
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    body1: {
      fontFamily: 'PT Sans, sans-serif',
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontFamily: 'PT Sans, sans-serif',
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          fontWeight: 600,
          padding: '10px 24px',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 10px 25px rgba(139, 69, 19, 0.3)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: '#1a1a1a',
          borderRadius: 12,
          border: '1px solid #404040',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
          },
        },
      },
    },
  },
});

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  return (
    <ConfigProvider theme={antdTheme}>
      <MuiThemeProvider theme={muiTheme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </ConfigProvider>
  );
}
