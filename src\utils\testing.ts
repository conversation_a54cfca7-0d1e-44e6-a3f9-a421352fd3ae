// Testing utilities and checklist for VERTEX FORGE landing page

export interface TestResult {
  category: string;
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: string;
}

export class LandingPageTester {
  private results: TestResult[] = [];

  // Test responsive design
  async testResponsiveDesign(): Promise<TestResult[]> {
    const breakpoints = [
      { name: 'Mobile', width: 375 },
      { name: 'Mobile Large', width: 414 },
      { name: 'Tablet', width: 768 },
      { name: 'Desktop Small', width: 1024 },
      { name: 'Desktop Large', width: 1440 },
      { name: 'Desktop XL', width: 1920 }
    ];

    const results: TestResult[] = [];

    for (const breakpoint of breakpoints) {
      // Simulate viewport resize
      const originalWidth = window.innerWidth;
      
      try {
        // Test if elements are properly responsive
        const heroTitle = document.querySelector('.hero-title-gradient');
        const navMenu = document.querySelector('.nav-menu');
        const featureCards = document.querySelectorAll('.feature-card');

        if (breakpoint.width < 768) {
          // Mobile tests
          results.push({
            category: 'Responsive',
            test: `${breakpoint.name} - Hero title readable`,
            status: heroTitle ? 'pass' : 'fail',
            message: heroTitle ? 'Hero title found and should be readable on mobile' : 'Hero title not found'
          });

          results.push({
            category: 'Responsive',
            test: `${breakpoint.name} - Navigation mobile-friendly`,
            status: navMenu ? 'pass' : 'warning',
            message: 'Navigation should be mobile-optimized'
          });
        } else {
          // Desktop tests
          results.push({
            category: 'Responsive',
            test: `${breakpoint.name} - Feature cards layout`,
            status: featureCards.length > 0 ? 'pass' : 'fail',
            message: `Found ${featureCards.length} feature cards`
          });
        }
      } catch (error) {
        results.push({
          category: 'Responsive',
          test: `${breakpoint.name} - Test execution`,
          status: 'fail',
          message: `Error testing ${breakpoint.name}: ${error}`
        });
      }
    }

    return results;
  }

  // Test animation performance
  testAnimationPerformance(): TestResult[] {
    const results: TestResult[] = [];

    // Check if GSAP is loaded
    if (typeof window !== 'undefined' && (window as any).gsap) {
      results.push({
        category: 'Animation',
        test: 'GSAP Library Loaded',
        status: 'pass',
        message: 'GSAP animation library is properly loaded'
      });

      // Check for performance-optimized animations
      const animatedElements = document.querySelectorAll('[data-animate]');
      results.push({
        category: 'Animation',
        test: 'Animated Elements Found',
        status: animatedElements.length > 0 ? 'pass' : 'warning',
        message: `Found ${animatedElements.length} elements with animation attributes`
      });

      // Check for will-change CSS property
      const elementsWithWillChange = document.querySelectorAll('[style*="will-change"]');
      results.push({
        category: 'Animation',
        test: 'Performance Optimization',
        status: elementsWithWillChange.length > 0 ? 'pass' : 'warning',
        message: `${elementsWithWillChange.length} elements optimized with will-change`
      });
    } else {
      results.push({
        category: 'Animation',
        test: 'GSAP Library Loaded',
        status: 'fail',
        message: 'GSAP animation library not found'
      });
    }

    return results;
  }

  // Test accessibility features
  testAccessibility(): TestResult[] {
    const results: TestResult[] = [];

    // Check for skip link
    const skipLink = document.querySelector('a[href="#main-content"]');
    results.push({
      category: 'Accessibility',
      test: 'Skip to Main Content Link',
      status: skipLink ? 'pass' : 'fail',
      message: skipLink ? 'Skip link found' : 'Skip link missing'
    });

    // Check for main content landmark
    const mainContent = document.querySelector('#main-content, main');
    results.push({
      category: 'Accessibility',
      test: 'Main Content Landmark',
      status: mainContent ? 'pass' : 'fail',
      message: mainContent ? 'Main content landmark found' : 'Main content landmark missing'
    });

    // Check for alt text on images
    const images = document.querySelectorAll('img');
    const imagesWithoutAlt = Array.from(images).filter(img => !img.alt);
    results.push({
      category: 'Accessibility',
      test: 'Image Alt Text',
      status: imagesWithoutAlt.length === 0 ? 'pass' : 'warning',
      message: `${imagesWithoutAlt.length} images missing alt text out of ${images.length} total`
    });

    // Check for proper heading hierarchy
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const h1Count = document.querySelectorAll('h1').length;
    results.push({
      category: 'Accessibility',
      test: 'Heading Hierarchy',
      status: h1Count === 1 ? 'pass' : 'warning',
      message: `Found ${h1Count} h1 elements (should be 1), ${headings.length} total headings`
    });

    // Check for focus indicators
    const focusableElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]');
    results.push({
      category: 'Accessibility',
      test: 'Focusable Elements',
      status: focusableElements.length > 0 ? 'pass' : 'warning',
      message: `Found ${focusableElements.length} focusable elements`
    });

    return results;
  }

  // Test SEO elements
  testSEO(): TestResult[] {
    const results: TestResult[] = [];

    // Check for title tag
    const title = document.querySelector('title');
    results.push({
      category: 'SEO',
      test: 'Page Title',
      status: title && title.textContent ? 'pass' : 'fail',
      message: title ? `Title: "${title.textContent}"` : 'Page title missing'
    });

    // Check for meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    results.push({
      category: 'SEO',
      test: 'Meta Description',
      status: metaDescription ? 'pass' : 'fail',
      message: metaDescription ? 'Meta description found' : 'Meta description missing'
    });

    // Check for Open Graph tags
    const ogTitle = document.querySelector('meta[property="og:title"]');
    const ogDescription = document.querySelector('meta[property="og:description"]');
    const ogImage = document.querySelector('meta[property="og:image"]');
    
    results.push({
      category: 'SEO',
      test: 'Open Graph Tags',
      status: ogTitle && ogDescription && ogImage ? 'pass' : 'warning',
      message: `OG tags found: ${[ogTitle, ogDescription, ogImage].filter(Boolean).length}/3`
    });

    // Check for structured data
    const structuredData = document.querySelector('script[type="application/ld+json"]');
    results.push({
      category: 'SEO',
      test: 'Structured Data',
      status: structuredData ? 'pass' : 'warning',
      message: structuredData ? 'Structured data found' : 'Structured data missing'
    });

    return results;
  }

  // Test performance metrics
  testPerformance(): TestResult[] {
    const results: TestResult[] = [];

    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      // Check DOM Content Loaded time
      const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
      results.push({
        category: 'Performance',
        test: 'DOM Content Loaded',
        status: domContentLoaded < 1500 ? 'pass' : 'warning',
        message: `${domContentLoaded.toFixed(2)}ms (target: <1500ms)`
      });

      // Check for resource hints
      const preconnectLinks = document.querySelectorAll('link[rel="preconnect"]');
      const dnsPrefetchLinks = document.querySelectorAll('link[rel="dns-prefetch"]');
      
      results.push({
        category: 'Performance',
        test: 'Resource Hints',
        status: preconnectLinks.length > 0 || dnsPrefetchLinks.length > 0 ? 'pass' : 'warning',
        message: `${preconnectLinks.length} preconnect, ${dnsPrefetchLinks.length} dns-prefetch links`
      });

      // Check for font loading optimization
      const fontPreloads = document.querySelectorAll('link[rel="preload"][as="font"]');
      results.push({
        category: 'Performance',
        test: 'Font Optimization',
        status: fontPreloads.length > 0 ? 'pass' : 'warning',
        message: `${fontPreloads.length} fonts preloaded`
      });
    } else {
      results.push({
        category: 'Performance',
        test: 'Performance API',
        status: 'fail',
        message: 'Performance API not available'
      });
    }

    return results;
  }

  // Run all tests
  async runAllTests(): Promise<TestResult[]> {
    const allResults: TestResult[] = [];

    try {
      const responsiveResults = await this.testResponsiveDesign();
      const animationResults = this.testAnimationPerformance();
      const accessibilityResults = this.testAccessibility();
      const seoResults = this.testSEO();
      const performanceResults = this.testPerformance();

      allResults.push(
        ...responsiveResults,
        ...animationResults,
        ...accessibilityResults,
        ...seoResults,
        ...performanceResults
      );
    } catch (error) {
      allResults.push({
        category: 'System',
        test: 'Test Execution',
        status: 'fail',
        message: `Error running tests: ${error}`
      });
    }

    this.results = allResults;
    return allResults;
  }

  // Generate test report
  generateReport(): string {
    const passCount = this.results.filter(r => r.status === 'pass').length;
    const warningCount = this.results.filter(r => r.status === 'warning').length;
    const failCount = this.results.filter(r => r.status === 'fail').length;

    let report = `VERTEX FORGE Landing Page Test Report\n`;
    report += `=====================================\n\n`;
    report += `Summary: ${passCount} passed, ${warningCount} warnings, ${failCount} failed\n\n`;

    const categories = [...new Set(this.results.map(r => r.category))];
    
    categories.forEach(category => {
      report += `${category}:\n`;
      report += `${'-'.repeat(category.length + 1)}\n`;
      
      const categoryResults = this.results.filter(r => r.category === category);
      categoryResults.forEach(result => {
        const status = result.status === 'pass' ? '✓' : result.status === 'warning' ? '⚠' : '✗';
        report += `${status} ${result.test}: ${result.message}\n`;
      });
      report += '\n';
    });

    return report;
  }
}
