
'use client';

import Link from 'next/link';
import { Briefcase } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useEffect, useState, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Button as AntButton, Drawer, Space } from 'antd';
import { MenuOutlined, LoginOutlined, UserAddOutlined, HomeOutlined } from '@ant-design/icons';
import '@/styles/hero.css';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

const navLinks = [
  { href: '/#features', label: 'Features', icon: HomeOutlined },
  { href: '/#testimonials', label: 'Testimonials', icon: HomeOutlined },
  { href: '/freelancer/projects', label: 'Find Work', icon: HomeOutlined },
  { href: '/client/freelancers', label: 'Find Talent', icon: HomeOutlined },
];

// Navigation link component with hover animations
function NavLink({ href, label, isActive }: { href: string; label: string; isActive: boolean }) {
  const linkRef = useRef<HTMLAnchorElement>(null);

  useEffect(() => {
    if (!linkRef.current) return;

    const link = linkRef.current;

    const handleMouseEnter = () => {
      gsap.to(link, {
        scale: 1.05,
        y: -2,
        duration: 0.3,
        ease: 'power2.out',
      });
    };

    const handleMouseLeave = () => {
      gsap.to(link, {
        scale: 1,
        y: 0,
        duration: 0.3,
        ease: 'power2.out',
      });
    };

    link.addEventListener('mouseenter', handleMouseEnter);
    link.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      link.removeEventListener('mouseenter', handleMouseEnter);
      link.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return (
    <Link
      ref={linkRef}
      href={href}
      className={cn(
        'relative px-4 py-2 text-sm font-medium transition-all duration-300 uppercase tracking-wide',
        'before:absolute before:bottom-0 before:left-0 before:h-0.5 before:w-0 before:bg-gradient-to-r before:from-amber-600 before:to-amber-700 before:transition-all before:duration-300',
        'hover:before:w-full hover:text-amber-400',
        isActive
          ? 'text-amber-400 before:w-full'
          : 'text-gray-300 hover:text-white'
      )}
      prefetch={false}
    >
      {label}
    </Link>
  );
}

// Logo component with animation
function Logo() {
  const logoRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!logoRef.current) return;

    const logo = logoRef.current;

    const handleMouseEnter = () => {
      gsap.to(logo.querySelector('.logo-icon'), {
        rotation: 15,
        scale: 1.1,
        duration: 0.3,
        ease: 'power2.out',
      });
    };

    const handleMouseLeave = () => {
      gsap.to(logo.querySelector('.logo-icon'), {
        rotation: 0,
        scale: 1,
        duration: 0.3,
        ease: 'power2.out',
      });
    };

    logo.addEventListener('mouseenter', handleMouseEnter);
    logo.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      logo.removeEventListener('mouseenter', handleMouseEnter);
      logo.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return (
    <Link href="/" className="flex items-center gap-3" prefetch={false}>
      <div ref={logoRef} className="flex items-center gap-3">
        <div className="logo-icon relative">
          <Briefcase className="h-8 w-8 text-amber-600 drop-shadow-lg" />
          <div className="absolute inset-0 bg-amber-600/20 rounded-full blur-lg -z-10" />
        </div>
        <span className="text-2xl font-bold font-brand text-gradient-white tracking-wider">
          VERTEX FORGE
        </span>
      </div>
    </Link>
  );
}

export function Header() {
  const pathname = usePathname();
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const headerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY > 10;
      setIsScrolled(scrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (!headerRef.current) return;

    // Initial header animation
    gsap.fromTo(headerRef.current,
      { y: -100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: 'power3.out', delay: 0.5 }
    );

    // Animate navigation links
    gsap.fromTo('.nav-link',
      { opacity: 0, y: -20 },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: 'power2.out',
        delay: 1
      }
    );

    // Animate action buttons
    gsap.fromTo('.header-action-btn',
      { opacity: 0, scale: 0.8 },
      {
        opacity: 1,
        scale: 1,
        duration: 0.5,
        stagger: 0.1,
        ease: 'back.out(1.7)',
        delay: 1.3
      }
    );
  }, []);

  return (
    <header
      ref={headerRef}
      className={cn(
        "fixed top-0 z-50 w-full transition-all duration-500",
        isScrolled
          ? "glass-morphism-dark border-b border-white/10 shadow-2xl"
          : "bg-transparent"
      )}
    >
      <div className="container mx-auto flex h-20 items-center justify-between px-4 md:px-6">
        {/* Logo */}
        <Logo />

        {/* Desktop Navigation */}
        <nav className="hidden items-center gap-8 md:flex">
          {navLinks.map((link) => (
            <div key={link.href} className="nav-link">
              <NavLink
                href={link.href}
                label={link.label}
                isActive={pathname === link.href}
              />
            </div>
          ))}
        </nav>

        {/* Desktop Action Buttons */}
        <div className="hidden items-center gap-4 md:flex">
          <AntButton
            type="text"
            icon={<LoginOutlined />}
            className="header-action-btn h-10 px-6 text-white border-white/20 hover:bg-white/10 hover:border-white/30 transition-all duration-300"
          >
            <Link href="/login" className="text-white">LOGIN</Link>
          </AntButton>
          <AntButton
            type="primary"
            icon={<UserAddOutlined />}
            className="header-action-btn h-10 px-6 bg-gradient-to-r from-amber-600 to-amber-700 border-none hover:from-amber-500 hover:to-amber-600 shadow-lg hover:shadow-amber-500/25 transition-all duration-300"
            style={{ borderRadius: '8px' }}
          >
            <Link href="/signup" className="text-white">SIGN UP</Link>
          </AntButton>
        </div>

        {/* Mobile Menu Button */}
        <AntButton
          type="text"
          icon={<MenuOutlined />}
          className="md:hidden text-white hover:bg-white/10 border-white/20"
          onClick={() => setMobileMenuOpen(true)}
        />

        {/* Mobile Drawer */}
        <Drawer
          title={
            <div className="flex items-center gap-3">
              <Briefcase className="h-6 w-6 text-amber-600" />
              <span className="text-lg font-bold text-white">VERTEX FORGE</span>
            </div>
          }
          placement="right"
          onClose={() => setMobileMenuOpen(false)}
          open={mobileMenuOpen}
          className="mobile-drawer"
          styles={{
            body: {
              background: 'linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%)',
              padding: '24px',
            },
            header: {
              background: 'linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%)',
              borderBottom: '1px solid #404040',
            }
          }}
        >
          <div className="flex flex-col gap-6">
            {/* Mobile Navigation Links */}
            <nav className="flex flex-col gap-4">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="text-lg font-medium text-gray-300 hover:text-amber-400 transition-colors duration-300 py-2"
                  onClick={() => setMobileMenuOpen(false)}
                  prefetch={false}
                >
                  {link.label}
                </Link>
              ))}
            </nav>

            {/* Mobile Action Buttons */}
            <Space direction="vertical" size="middle" className="w-full">
              <AntButton
                type="text"
                icon={<LoginOutlined />}
                className="w-full h-12 text-white border-white/20 hover:bg-white/10"
                onClick={() => setMobileMenuOpen(false)}
              >
                <Link href="/login">LOGIN</Link>
              </AntButton>
              <AntButton
                type="primary"
                icon={<UserAddOutlined />}
                className="w-full h-12 bg-gradient-to-r from-amber-600 to-amber-700 border-none"
                onClick={() => setMobileMenuOpen(false)}
              >
                <Link href="/signup">SIGN UP</Link>
              </AntButton>
            </Space>
          </div>
        </Drawer>
      </div>
    </header>
  );
}
