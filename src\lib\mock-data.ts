import type { Project, FreelancerProfile } from './types';

export const mockProjects: Project[] = [
  {
    id: '1',
    title: 'E-commerce Website Redesign',
    description: 'Looking for an experienced UI/UX designer to redesign our Shopify store. Must have a strong portfolio in e-commerce.',
    budget: 5000,
    skills: ['UI/UX Design', 'Shopify', 'Figma'],
    status: 'open',
    clientId: '101',
    clientName: 'GlobalMart Inc.',
  },
  {
    id: '2',
    title: 'Mobile App Development for Fitness Startup',
    description: 'We need a React Native developer to build our MVP. The app will include user authentication, workout tracking, and social features.',
    budget: 15000,
    skills: ['React Native', 'Firebase', 'Node.js'],
    status: 'open',
    clientId: '102',
    clientName: 'FitLife Co.',
  },
  {
    id: '3',
    title: 'Content Writer for Tech Blog',
    description: 'Seeking a skilled writer to produce 4 high-quality blog posts per month on topics related to AI and machine learning.',
    budget: 800,
    skills: ['Content Writing', 'SEO', 'AI'],
    status: 'in-progress',
    clientId: '103',
    clientName: 'Innovate Today',
  },
  {
    id: '4',
    title: 'Build a custom CRM with Next.js',
    description: 'We need a full-stack developer to build a CRM for our sales team. Tech stack: Next.js, Tailwind CSS, PostgreSQL.',
    budget: 20000,
    skills: ['Next.js', 'Tailwind CSS', 'PostgreSQL', 'Prisma'],
    status: 'open',
    clientId: '101',
    clientName: 'GlobalMart Inc.',
  },
];


export const mockFreelancers: FreelancerProfile[] = [
  {
    id: '201',
    name: 'Alice Johnson',
    email: '<EMAIL>',
    avatarUrl: 'https://placehold.co/100x100.png',
    role: 'freelancer',
    isVerified: true,
    title: 'Senior UI/UX Designer',
    skills: ['UI/UX Design', 'Figma', 'Webflow', 'Branding'],
    bio: '10+ years of experience in creating beautiful and user-friendly digital experiences. Passionate about clean design and great usability.',
    rating: 4.9,
    hourlyRate: 95,
    portfolio: [
        { title: 'Fintech App Design', imageUrl: 'https://placehold.co/600x400.png'},
        { title: 'SaaS Platform Redesign', imageUrl: 'https://placehold.co/600x400.png'},
    ]
  },
  {
    id: '202',
    name: 'Bob Williams',
    email: '<EMAIL>',
    avatarUrl: 'https://placehold.co/100x100.png',
    role: 'freelancer',
    isVerified: true,
    title: 'Full-Stack Developer (React & Node)',
    skills: ['React', 'Node.js', 'TypeScript', 'AWS'],
    bio: 'I build scalable and robust web applications from scratch. Expertise in the MERN stack and cloud deployment on AWS.',
    rating: 5.0,
    hourlyRate: 120,
    portfolio: [
        { title: 'Real-time Chat App', imageUrl: 'https://placehold.co/600x400.png' },
        { title: 'E-commerce API', imageUrl: 'https://placehold.co/600x400.png' },
    ]
  },
  {
    id: '203',
    name: 'Charlie Brown',
    email: '<EMAIL>',
    avatarUrl: 'https://placehold.co/100x100.png',
    role: 'freelancer',
    isVerified: false,
    title: 'SEO & Content Strategist',
    skills: ['SEO', 'Content Writing', 'Google Analytics', 'Marketing'],
    bio: 'Helping businesses grow their organic traffic through data-driven SEO and compelling content.',
    rating: 4.8,
    hourlyRate: 70,
    portfolio: [
        { title: 'Blog Traffic Growth Case Study', imageUrl: 'https://placehold.co/600x400.png' },
        { title: 'Keyword Research Report', imageUrl: 'https://placehold.co/600x400.png' },
    ]
  },
];
