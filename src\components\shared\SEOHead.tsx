import Head from 'next/head';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  ogUrl?: string;
  twitterCard?: 'summary' | 'summary_large_image';
  canonicalUrl?: string;
  structuredData?: object;
}

export function SEOHead({
  title = 'VERTEX FORGE - Professional Networking & Business Platform',
  description = 'Join VERTEX FORGE, the premier platform for professionals to build their businesses, find opportunities, and connect with industry leaders. Advanced features, modern design, and powerful networking tools.',
  keywords = 'professional networking, business platform, career opportunities, industry connections, business growth, professional development, networking platform, business tools',
  ogImage = '/og-image.jpg',
  ogUrl = 'https://vertexforge.com',
  twitterCard = 'summary_large_image',
  canonicalUrl,
  structuredData
}: SEOHeadProps) {
  const fullTitle = title.includes('VERTEX FORGE') ? title : `${title} | VERTEX FORGE`;
  
  const defaultStructuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "VERTEX FORGE",
    "description": description,
    "url": ogUrl,
    "logo": `${ogUrl}/logo.png`,
    "sameAs": [
      "https://twitter.com/vertexforge",
      "https://linkedin.com/company/vertexforge",
      "https://facebook.com/vertexforge"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-VERTEX",
      "contactType": "customer service",
      "availableLanguage": ["English"]
    }
  };

  const finalStructuredData = structuredData || defaultStructuredData;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="VERTEX FORGE" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="robots" content="index, follow" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:url" content={ogUrl} />
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content="VERTEX FORGE" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      <meta name="twitter:site" content="@vertexforge" />
      <meta name="twitter:creator" content="@vertexforge" />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      <meta name="theme-color" content="#000000" />
      <meta name="msapplication-TileColor" content="#000000" />
      
      {/* Performance and Security */}
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      
      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(finalStructuredData)
        }}
      />
      
      {/* Additional Performance Hints */}
      <link rel="preload" href="/fonts/playfair-display.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
      <link rel="preload" href="/fonts/pt-sans.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
    </Head>
  );
}

// Specific SEO components for different pages
export function HomePageSEO() {
  return (
    <SEOHead
      title="VERTEX FORGE - Professional Networking & Business Platform"
      description="Join VERTEX FORGE, the premier platform for professionals to build their businesses, find opportunities, and connect with industry leaders. Advanced features, modern design, and powerful networking tools."
      keywords="professional networking, business platform, career opportunities, industry connections, business growth, professional development, networking platform, business tools, vertex forge"
      structuredData={{
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "VERTEX FORGE",
        "url": "https://vertexforge.com",
        "description": "Professional networking and business platform for industry leaders",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://vertexforge.com/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      }}
    />
  );
}

export function FeaturesPageSEO() {
  return (
    <SEOHead
      title="Features - Advanced Professional Networking Tools"
      description="Discover VERTEX FORGE's powerful features: secure messaging, smart matching, business analytics, and more. Everything you need to grow your professional network and business."
      keywords="networking features, business tools, professional platform features, secure messaging, business analytics, smart matching"
    />
  );
}

export function AboutPageSEO() {
  return (
    <SEOHead
      title="About VERTEX FORGE - Our Mission & Vision"
      description="Learn about VERTEX FORGE's mission to revolutionize professional networking. Our story, values, and commitment to connecting professionals worldwide."
      keywords="about vertex forge, company mission, professional networking vision, team, company values"
    />
  );
}
