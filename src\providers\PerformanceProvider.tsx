'use client';

import { useEffect } from 'react';
import { 
  PerformanceMonitor, 
  optimizeGSAPForPerformance, 
  addResourceHints,
  checkPerformanceBudget 
} from '@/utils/performance';

interface PerformanceProviderProps {
  children: React.ReactNode;
}

export function PerformanceProvider({ children }: PerformanceProviderProps) {
  useEffect(() => {
    // Initialize performance monitoring
    const monitor = PerformanceMonitor.getInstance();
    
    // Set up Core Web Vitals monitoring
    monitor.initCoreWebVitals();
    
    // Optimize GSAP for better performance
    optimizeGSAPForPerformance();
    
    // Add resource hints for better loading performance
    addResourceHints();
    
    // Check performance budget after page load
    const checkBudget = () => {
      setTimeout(() => {
        checkPerformanceBudget();
      }, 3000); // Wait 3 seconds after load to get accurate metrics
    };
    
    if (document.readyState === 'complete') {
      checkBudget();
    } else {
      window.addEventListener('load', checkBudget);
    }
    
    // Cleanup function
    return () => {
      monitor.cleanup();
      window.removeEventListener('load', checkBudget);
    };
  }, []);

  return <>{children}</>;
}
