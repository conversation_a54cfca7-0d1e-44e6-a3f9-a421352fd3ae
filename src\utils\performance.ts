'use client';

import { gsap } from 'gsap';

// Performance monitoring utilities
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number> = new Map();
  private observers: Map<string, PerformanceObserver> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Measure animation performance
  measureAnimation(name: string, animationFn: () => void): void {
    const startTime = performance.now();
    
    animationFn();
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.metrics.set(`animation_${name}`, duration);
    
    if (duration > 16.67) { // More than one frame at 60fps
      console.warn(`Animation "${name}" took ${duration.toFixed(2)}ms (> 16.67ms)`);
    }
  }

  // Monitor Core Web Vitals
  initCoreWebVitals(): void {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        const lcp = lastEntry.startTime;
        
        this.metrics.set('lcp', lcp);
        
        if (lcp > 2500) {
          console.warn(`LCP is ${lcp.toFixed(2)}ms (should be < 2500ms)`);
        }
      });
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.set('lcp', lcpObserver);

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          const fid = entry.processingStart - entry.startTime;
          this.metrics.set('fid', fid);
          
          if (fid > 100) {
            console.warn(`FID is ${fid.toFixed(2)}ms (should be < 100ms)`);
          }
        });
      });
      
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.set('fid', fidObserver);

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        
        this.metrics.set('cls', clsValue);
        
        if (clsValue > 0.1) {
          console.warn(`CLS is ${clsValue.toFixed(3)} (should be < 0.1)`);
        }
      });
      
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.set('cls', clsObserver);
    }
  }

  // Get performance metrics
  getMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics);
  }

  // Clean up observers
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }
}

// Lazy loading utilities
export function createIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  };

  return new IntersectionObserver(callback, defaultOptions);
}

// Image optimization utilities
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

export function preloadImages(srcs: string[]): Promise<void[]> {
  return Promise.all(srcs.map(preloadImage));
}

// Animation optimization
export function optimizeGSAPForPerformance(): void {
  // Set GSAP to use CSS transforms for better performance
  gsap.config({
    force3D: true,
    nullTargetWarn: false,
    trialWarn: false
  });

  // Use will-change CSS property for elements that will be animated
  gsap.set('[data-animate]', { willChange: 'transform' });
}

// Debounce utility for scroll and resize events
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle utility for high-frequency events
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Resource hints utilities
export function addResourceHints(): void {
  const head = document.head;

  // Preconnect to external domains
  const preconnectDomains = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
    'https://cdnjs.cloudflare.com'
  ];

  preconnectDomains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = domain;
    link.crossOrigin = 'anonymous';
    head.appendChild(link);
  });

  // DNS prefetch for other domains
  const dnsPrefetchDomains = [
    'https://api.example.com',
    'https://analytics.example.com'
  ];

  dnsPrefetchDomains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = domain;
    head.appendChild(link);
  });
}

// Memory management for animations
export function cleanupAnimations(selector: string): void {
  gsap.killTweensOf(selector);
  gsap.set(selector, { clearProps: 'all' });
}

// Responsive image loading
export function getOptimalImageSrc(
  baseSrc: string,
  width: number,
  devicePixelRatio: number = window.devicePixelRatio || 1
): string {
  const optimalWidth = Math.ceil(width * devicePixelRatio);
  
  // Return appropriate image size based on width
  if (optimalWidth <= 480) return `${baseSrc}?w=480&q=75`;
  if (optimalWidth <= 768) return `${baseSrc}?w=768&q=80`;
  if (optimalWidth <= 1024) return `${baseSrc}?w=1024&q=85`;
  if (optimalWidth <= 1440) return `${baseSrc}?w=1440&q=90`;
  return `${baseSrc}?w=1920&q=95`;
}

// Bundle size optimization - dynamic imports
export async function loadComponentDynamically<T>(
  importFn: () => Promise<{ default: T }>
): Promise<T> {
  try {
    const module = await importFn();
    return module.default;
  } catch (error) {
    console.error('Failed to load component dynamically:', error);
    throw error;
  }
}

// Performance budget checker
export function checkPerformanceBudget(): void {
  if ('performance' in window && 'getEntriesByType' in performance) {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    const metrics = {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      firstPaint: 0,
      firstContentfulPaint: 0
    };

    // Get paint metrics
    const paintEntries = performance.getEntriesByType('paint');
    paintEntries.forEach((entry) => {
      if (entry.name === 'first-paint') {
        metrics.firstPaint = entry.startTime;
      } else if (entry.name === 'first-contentful-paint') {
        metrics.firstContentfulPaint = entry.startTime;
      }
    });

    // Check against budgets
    const budgets = {
      domContentLoaded: 1500, // 1.5s
      loadComplete: 3000,     // 3s
      firstPaint: 1000,       // 1s
      firstContentfulPaint: 1500 // 1.5s
    };

    Object.entries(metrics).forEach(([metric, value]) => {
      const budget = budgets[metric as keyof typeof budgets];
      if (value > budget) {
        console.warn(`Performance budget exceeded for ${metric}: ${value.toFixed(2)}ms (budget: ${budget}ms)`);
      }
    });
  }
}
