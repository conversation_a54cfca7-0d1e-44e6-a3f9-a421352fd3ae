# **App Name**: LancerLink

## Core Features:

- Landing Page Experience: Visually stunning and animated landing page with clear calls-to-action for Clients and Freelancers.
- User Authentication and Verification: Role-based registration/login flows with Aadhar verification UI (upload and progress indicator).
- Role-Based Dashboards: Dedicated dashboards for Clients and Freelancers, featuring key functionalities.
- Enhanced Search and Discovery: Rich search and filtering options to browse through Freelancers/Projects with an eye-catching listing display and freelancer verified status (via Aadhar verification).
- Real-Time Messaging: Real-time messaging interface to connect and collaborate between Clients and Freelancers. Note: This feature requires back-end support which must be created later.
- Universal Help Chatbot: Every page offers a Help Chatbot, offering contextual support for both clients and freelancers
- AI-Powered Help Chatbot: AI-powered chatbot tool for "finding genuine clients," profile tips, and navigating opportunities, especially for freelancers. It also suggests relevant matches (freelancers for projects, projects for freelancers).

## Style Guidelines:

- Primary color: Slate blue (#7395AE) to evoke professionalism and trust in a tech-forward manner. This color is suitable for the user base of professional freelancers.
- Background color: Light gray (#E2DDCB), a desaturated tint of slate blue, will create a neutral and professional background.
- Accent color: Coral orange (#F0736A) creates visual contrast against the slate and a visual call-to-action in certain situations.
- Headline font: 'Playfair', a modern sans-serif with geometric shapes. Body font: 'PT Sans' for longer paragraphs
- Crisp, professional icons from Material UI to maintain a polished UI.
- Responsive, grid-based layout with clear visual hierarchy for easy navigation.
- GSAP and Framer Motion create seamless page transitions and subtle UI animations, including loading and button hovering.