import Link from "next/link";
import { Briefcase } from "lucide-react";

export function Footer() {
  return (
    <footer className="bg-muted py-8 px-4 md:px-6">
      <div className="container mx-auto grid grid-cols-1 md:grid-cols-4 gap-8">
        <div className="flex flex-col gap-4">
          <Link href="/" className="flex items-center gap-2" prefetch={false}>
            <Briefcase className="h-7 w-7 text-primary" />
            <span className="text-2xl font-bold font-headline">LancerLink</span>
          </Link>
          <p className="text-muted-foreground text-sm">
            Uniting clients and top talent in one place.
          </p>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 col-span-1 md:col-span-3 gap-8">
            <div>
              <h4 className="font-semibold mb-3">For Clients</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="/client/freelancers" className="text-muted-foreground hover:text-primary">Find Talent</Link></li>
                <li><Link href="/client/projects/new" className="text-muted-foreground hover:text-primary">Post a Project</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-primary">How it Works</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">For Freelancers</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="/freelancer/projects" className="text-muted-foreground hover:text-primary">Find Work</Link></li>
                <li><Link href="/signup" className="text-muted-foreground hover:text-primary">Create Profile</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-primary">Community</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">Company</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="#" className="text-muted-foreground hover:text-primary">About Us</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-primary">Careers</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-primary">Contact</Link></li>
              </ul>
            </div>
        </div>
      </div>
      <div className="container mx-auto mt-8 pt-4 border-t border-border/50 text-center text-sm text-muted-foreground">
        © {new Date().getFullYear()} LancerLink. All rights reserved.
      </div>
    </footer>
  );
}
