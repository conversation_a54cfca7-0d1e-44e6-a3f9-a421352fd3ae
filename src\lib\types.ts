export type User = {
  id: string;
  name: string;
  email: string;
  avatarUrl: string;
  role: 'client' | 'freelancer';
  isVerified: boolean;
};

export type Project = {
  id: string;
  title: string;
  description: string;
  budget: number;
  skills: string[];
  status: 'open' | 'in-progress' | 'completed';
  clientId: string;
  clientName: string;
};

export type FreelancerProfile = User & {
  role: 'freelancer';
  title: string;
  skills: string[];
  bio: string;
  rating: number;
  hourlyRate: number;
  portfolio: {
    title: string;
    imageUrl: string;
  }[];
};

export type Message = {
    id: string;
    senderId: string;
    recipientId: string;
    text: string;
    timestamp: Date;
};
