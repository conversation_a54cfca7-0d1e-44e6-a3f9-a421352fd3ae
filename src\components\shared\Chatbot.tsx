'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from '@/components/ui/sheet';
import { Input } from '@/components/ui/input';
import { Bot, MessageSquare, Send, User } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { getFreelancerAdvice, FreelancerAdviceInput } from '@/ai/flows/freelancer-advice';
import { suggestMatches, SuggestMatchesInput } from '@/ai/flows/suggest-matches';

type Message = {
  text: string;
  isUser: boolean;
};

export function Chatbot() {
  const [messages, setMessages] = useState<Message[]>([
    { text: "Hello! I'm the LancerLink AI assistant. How can I help you today? You can ask for profile advice or to find matches for a project.", isUser: false },
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSend = async () => {
    if (input.trim() === '') return;
    
    const userMessage: Message = { text: input, isUser: true };
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Simple logic to decide which AI flow to call
      if (input.toLowerCase().includes('advice') || input.toLowerCase().includes('profile')) {
        const adviceInput: FreelancerAdviceInput = {
          profileDetails: 'A freelance web developer with 3 years of experience in React and Node.js.',
          desiredProjects: 'Looking for long-term projects building SaaS applications.',
        };
        const res = await getFreelancerAdvice(adviceInput);
        const aiResponse: Message = { text: `**Profile Tips:**\n${res.profileTips}\n\n**Client Finding Advice:**\n${res.clientFindingAdvice}`, isUser: false };
        setMessages(prev => [...prev, aiResponse]);
      } else {
        const matchInput: SuggestMatchesInput = {
          userType: 'client',
          query: input,
          profile: 'A startup looking for a senior React developer for a 3-month contract.',
        };
        const res = await suggestMatches(matchInput);
        const aiResponse: Message = { text: `Here are some matches for you:\n- ${res.suggestions.join('\n- ')}`, isUser: false };
        setMessages(prev => [...prev, aiResponse]);
      }
    } catch (error) {
      const errorMessage: Message = { text: "Sorry, I encountered an error. Please try again.", isUser: false };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          className="fixed bottom-6 right-6 h-16 w-16 rounded-full shadow-lg"
          size="icon"
        >
          <MessageSquare className="h-8 w-8" />
        </Button>
      </SheetTrigger>
      <SheetContent className="flex flex-col p-0">
        <SheetHeader className="p-4 border-b">
          <SheetTitle className="flex items-center gap-2">
            <Bot /> AI Assistant
          </SheetTitle>
        </SheetHeader>
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages.map((message, index) => (
              <div key={index} className={cn('flex items-start gap-3', message.isUser && 'justify-end')}>
                {!message.isUser && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      <Bot className="h-5 w-5" />
                    </AvatarFallback>
                  </Avatar>
                )}
                <div
                  className={cn(
                    'max-w-xs rounded-lg p-3 text-sm',
                    message.isUser
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  )}
                  style={{ whiteSpace: 'pre-wrap' }}
                >
                  {message.text}
                </div>
                {message.isUser && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      <User className="h-5 w-5" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
             {isLoading && (
              <div className="flex items-start gap-3">
                 <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      <Bot className="h-5 w-5" />
                    </AvatarFallback>
                  </Avatar>
                <div className="bg-muted rounded-lg p-3">
                    <div className="flex items-center space-x-1">
                        <span className="h-2 w-2 bg-foreground/50 rounded-full animate-pulse [animation-delay:-0.3s]"></span>
                        <span className="h-2 w-2 bg-foreground/50 rounded-full animate-pulse [animation-delay:-0.15s]"></span>
                        <span className="h-2 w-2 bg-foreground/50 rounded-full animate-pulse"></span>
                    </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
        <SheetFooter className="p-4 border-t">
          <div className="flex w-full items-center space-x-2">
            <Input
              id="message"
              placeholder="Type your message..."
              className="flex-1"
              autoComplete="off"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSend()}
            />
            <Button type="submit" size="icon" onClick={handleSend} disabled={isLoading}>
              <Send className="h-4 w-4" />
              <span className="sr-only">Send</span>
            </Button>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
