'use client';

import { useState } from 'react';
import { Container, Typography, Box, Grid } from '@mui/material';
import { 
  LoadingSpinner, 
  SkeletonCard, 
  SkeletonTestimonial, 
  AnimatedProgress, 
  MicroButton, 
  PageTransition,
  HoverCard 
} from '@/components/shared/LoadingStates';
import { DefaultBackground } from '@/components/shared/BackgroundElements';

export function InteractionsDemo() {
  const [showTransition, setShowTransition] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleProgressDemo = () => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const handleTransitionDemo = () => {
    setShowTransition(true);
    setTimeout(() => setShowTransition(false), 2000);
  };

  return (
    <section className="relative min-h-screen py-20">
      <DefaultBackground />
      
      <Container maxWidth="lg" className="relative z-10">
        <Box sx={{ textAlign: 'center', mb: 8 }}>
          <Typography 
            variant="h2" 
            className="text-gradient-white font-headline mb-4"
            sx={{ fontSize: { xs: '2.5rem', md: '3.5rem' } }}
          >
            Loading States & Micro-interactions
          </Typography>
          <Typography 
            variant="h6" 
            className="text-gray-300 max-w-2xl mx-auto"
          >
            Showcase of smooth animations, loading states, and interactive elements
          </Typography>
        </Box>

        <Grid container spacing={4}>
          {/* Loading Spinners */}
          <Grid item xs={12} md={6}>
            <HoverCard className="glass-card-dark p-6 rounded-lg">
              <Typography variant="h5" className="text-amber-400 mb-4">
                Loading Spinners
              </Typography>
              <Box sx={{ display: 'flex', gap: 3, alignItems: 'center', flexWrap: 'wrap' }}>
                <LoadingSpinner size={30} />
                <LoadingSpinner size={40} color="#f59e0b" />
                <LoadingSpinner size={50} color="#d97706" />
              </Box>
            </HoverCard>
          </Grid>

          {/* Progress Bars */}
          <Grid item xs={12} md={6}>
            <HoverCard className="glass-card-dark p-6 rounded-lg">
              <Typography variant="h5" className="text-amber-400 mb-4">
                Animated Progress
              </Typography>
              <Box sx={{ mb: 3 }}>
                <AnimatedProgress value={progress} />
              </Box>
              <MicroButton onClick={handleProgressDemo} variant="secondary">
                Demo Progress
              </MicroButton>
            </HoverCard>
          </Grid>

          {/* Skeleton Cards */}
          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="h5" className="text-amber-400 mb-4">
                Skeleton Loaders
              </Typography>
              <SkeletonCard />
            </Box>
          </Grid>

          {/* Skeleton Testimonials */}
          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="h5" className="text-amber-400 mb-4">
                Testimonial Skeletons
              </Typography>
              <SkeletonTestimonial />
            </Box>
          </Grid>

          {/* Micro Buttons */}
          <Grid item xs={12}>
            <HoverCard className="glass-card-dark p-6 rounded-lg">
              <Typography variant="h5" className="text-amber-400 mb-4">
                Interactive Buttons
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <MicroButton variant="primary">
                  Primary Action
                </MicroButton>
                <MicroButton variant="secondary">
                  Secondary Action
                </MicroButton>
                <MicroButton variant="primary" disabled>
                  Disabled Button
                </MicroButton>
                <MicroButton variant="secondary" onClick={handleTransitionDemo}>
                  Page Transition Demo
                </MicroButton>
              </Box>
            </HoverCard>
          </Grid>

          {/* Hover Cards Demo */}
          <Grid item xs={12}>
            <Typography variant="h5" className="text-amber-400 mb-4">
              Hover Effects
            </Typography>
            <Grid container spacing={3}>
              {[1, 2, 3].map((item) => (
                <Grid item xs={12} sm={6} md={4} key={item}>
                  <HoverCard className="glass-card-dark p-6 rounded-lg">
                    <Typography variant="h6" className="text-white mb-2">
                      Hover Card {item}
                    </Typography>
                    <Typography className="text-gray-300">
                      Hover over this card to see the smooth lift animation with shadow effects.
                    </Typography>
                  </HoverCard>
                </Grid>
              ))}
            </Grid>
          </Grid>

          {/* Interactive Elements */}
          <Grid item xs={12}>
            <HoverCard className="glass-card-dark p-6 rounded-lg">
              <Typography variant="h5" className="text-amber-400 mb-4">
                Interactive Elements
              </Typography>
              <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap', alignItems: 'center' }}>
                <div className="floating-shape floating-circle w-16 h-16 animate-float-gentle" />
                <div className="floating-shape floating-square w-12 h-12 animate-float-medium" />
                <div className="floating-shape floating-triangle-up animate-float-strong" />
                <div className="particle" style={{ position: 'relative', display: 'inline-block' }} />
              </Box>
              <Typography className="text-gray-300 mt-4">
                Various animated geometric shapes and particles with different floating patterns.
              </Typography>
            </HoverCard>
          </Grid>
        </Grid>

        {/* Performance Tips */}
        <Box sx={{ mt: 8, textAlign: 'center' }}>
          <HoverCard className="glass-card-dark p-6 rounded-lg max-w-4xl mx-auto">
            <Typography variant="h5" className="text-amber-400 mb-4">
              Performance Optimizations
            </Typography>
            <Grid container spacing={3} sx={{ textAlign: 'left' }}>
              <Grid item xs={12} md={6}>
                <Typography className="text-white font-medium mb-2">
                  Animation Techniques:
                </Typography>
                <ul className="text-gray-300 space-y-1">
                  <li>• GSAP for high-performance animations</li>
                  <li>• Hardware acceleration with transform3d</li>
                  <li>• Optimized scroll triggers</li>
                  <li>• Reduced layout thrashing</li>
                </ul>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography className="text-white font-medium mb-2">
                  Loading Strategies:
                </Typography>
                <ul className="text-gray-300 space-y-1">
                  <li>• Progressive loading with skeletons</li>
                  <li>• Smooth state transitions</li>
                  <li>• Preloaded critical resources</li>
                  <li>• Lazy loading for non-critical content</li>
                </ul>
              </Grid>
            </Grid>
          </HoverCard>
        </Box>
      </Container>

      <PageTransition isVisible={showTransition} />
    </section>
  );
}
