'use client';

import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { Skeleton, Box, CircularProgress, LinearProgress } from '@mui/material';
import { Card as MuiCard, CardContent } from '@mui/material';
import '@/styles/hero.css';

// Loading spinner with GSAP animations
export function LoadingSpinner({ size = 40, color = '#d4af37' }: { size?: number; color?: string }) {
  const spinnerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!spinnerRef.current) return;

    const tl = gsap.timeline({ repeat: -1 });
    
    tl.to(spinnerRef.current, {
      rotation: 360,
      duration: 1,
      ease: 'none'
    });

    return () => tl.kill();
  }, []);

  return (
    <div className="flex items-center justify-center">
      <div
        ref={spinnerRef}
        className="border-2 border-transparent border-t-current rounded-full"
        style={{
          width: size,
          height: size,
          color: color,
          borderTopColor: color
        }}
      />
    </div>
  );
}

// Skeleton loader for cards
export function SkeletonCard() {
  return (
    <MuiCard className="glass-card-dark">
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Skeleton variant="circular" width={40} height={40} sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)' }} />
          <Box sx={{ ml: 2, flex: 1 }}>
            <Skeleton variant="text" width="60%" sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)' }} />
            <Skeleton variant="text" width="40%" sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)' }} />
          </Box>
        </Box>
        <Skeleton variant="rectangular" height={60} sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)' }} />
        <Box sx={{ mt: 2 }}>
          <Skeleton variant="text" width="80%" sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)' }} />
          <Skeleton variant="text" width="60%" sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)' }} />
        </Box>
      </CardContent>
    </MuiCard>
  );
}

// Skeleton loader for testimonials
export function SkeletonTestimonial() {
  return (
    <MuiCard className="glass-card-dark">
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Skeleton variant="circular" width={50} height={50} sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)' }} />
          <Box sx={{ ml: 2, flex: 1 }}>
            <Skeleton variant="text" width="70%" sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)' }} />
            <Skeleton variant="text" width="50%" sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)' }} />
          </Box>
        </Box>
        <Skeleton variant="rectangular" height={80} sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)', mb: 2 }} />
        <Skeleton variant="text" width="30%" sx={{ bgcolor: 'rgba(212, 175, 55, 0.1)' }} />
      </CardContent>
    </MuiCard>
  );
}

// Progress bar with animations
export function AnimatedProgress({ value = 0, showLabel = true }: { value?: number; showLabel?: boolean }) {
  const progressRef = useRef<HTMLDivElement>(null);
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    gsap.to({ value: displayValue }, {
      value: value,
      duration: 1.5,
      ease: 'power2.out',
      onUpdate: function() {
        setDisplayValue(Math.round(this.targets()[0].value));
      }
    });
  }, [value, displayValue]);

  return (
    <Box sx={{ width: '100%' }}>
      <LinearProgress
        variant="determinate"
        value={displayValue}
        sx={{
          height: 8,
          borderRadius: 4,
          backgroundColor: 'rgba(212, 175, 55, 0.2)',
          '& .MuiLinearProgress-bar': {
            backgroundColor: '#d4af37',
            borderRadius: 4,
          }
        }}
      />
      {showLabel && (
        <Box sx={{ mt: 1, textAlign: 'center' }}>
          <span className="text-amber-400 font-medium">{displayValue}%</span>
        </Box>
      )}
    </Box>
  );
}

// Micro-interaction button
export function MicroButton({ 
  children, 
  onClick, 
  variant = 'primary',
  disabled = false 
}: { 
  children: React.ReactNode; 
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}) {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!buttonRef.current) return;

    const button = buttonRef.current;

    const handleMouseEnter = () => {
      gsap.to(button, {
        scale: 1.05,
        y: -2,
        duration: 0.3,
        ease: 'power2.out'
      });
    };

    const handleMouseLeave = () => {
      gsap.to(button, {
        scale: 1,
        y: 0,
        duration: 0.3,
        ease: 'power2.out'
      });
    };

    const handleMouseDown = () => {
      gsap.to(button, {
        scale: 0.95,
        duration: 0.1,
        ease: 'power2.out'
      });
    };

    const handleMouseUp = () => {
      gsap.to(button, {
        scale: 1.05,
        duration: 0.1,
        ease: 'power2.out'
      });
    };

    button.addEventListener('mouseenter', handleMouseEnter);
    button.addEventListener('mouseleave', handleMouseLeave);
    button.addEventListener('mousedown', handleMouseDown);
    button.addEventListener('mouseup', handleMouseUp);

    return () => {
      button.removeEventListener('mouseenter', handleMouseEnter);
      button.removeEventListener('mouseleave', handleMouseLeave);
      button.removeEventListener('mousedown', handleMouseDown);
      button.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  const handleClick = async () => {
    if (disabled || isLoading) return;
    
    setIsLoading(true);
    
    // Simulate loading
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (onClick) onClick();
    setIsLoading(false);
  };

  const baseClasses = "relative px-6 py-3 rounded-lg font-medium transition-all duration-300 overflow-hidden";
  const variantClasses = variant === 'primary' 
    ? "bg-gradient-to-r from-amber-600 to-amber-700 text-white shadow-lg"
    : "bg-transparent border-2 border-amber-600 text-amber-400 hover:bg-amber-600/10";

  return (
    <button
      ref={buttonRef}
      onClick={handleClick}
      disabled={disabled || isLoading}
      className={`${baseClasses} ${variantClasses} ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
    >
      {isLoading ? (
        <div className="flex items-center justify-center">
          <LoadingSpinner size={20} color="currentColor" />
          <span className="ml-2">Loading...</span>
        </div>
      ) : (
        children
      )}
    </button>
  );
}

// Page transition overlay
export function PageTransition({ isVisible }: { isVisible: boolean }) {
  const overlayRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!overlayRef.current) return;

    if (isVisible) {
      gsap.fromTo(overlayRef.current, 
        { scaleX: 0, transformOrigin: 'left' },
        { scaleX: 1, duration: 0.5, ease: 'power2.inOut' }
      );
    } else {
      gsap.to(overlayRef.current, {
        scaleX: 0,
        transformOrigin: 'right',
        duration: 0.5,
        ease: 'power2.inOut'
      });
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div
      ref={overlayRef}
      className="fixed inset-0 bg-gradient-to-r from-black via-amber-900 to-black z-50 flex items-center justify-center"
    >
      <div className="text-center">
        <LoadingSpinner size={60} color="#d4af37" />
        <p className="text-white mt-4 text-lg">Loading...</p>
      </div>
    </div>
  );
}

// Hover card effect
export function HoverCard({ children, className = "" }: { children: React.ReactNode; className?: string }) {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!cardRef.current) return;

    const card = cardRef.current;

    const handleMouseEnter = () => {
      gsap.to(card, {
        y: -8,
        scale: 1.02,
        boxShadow: '0 20px 40px rgba(212, 175, 55, 0.2)',
        duration: 0.3,
        ease: 'power2.out'
      });
    };

    const handleMouseLeave = () => {
      gsap.to(card, {
        y: 0,
        scale: 1,
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
        duration: 0.3,
        ease: 'power2.out'
      });
    };

    card.addEventListener('mouseenter', handleMouseEnter);
    card.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      card.removeEventListener('mouseenter', handleMouseEnter);
      card.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return (
    <div ref={cardRef} className={`transition-all duration-300 ${className}`}>
      {children}
    </div>
  );
}
