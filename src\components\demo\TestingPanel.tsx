'use client';

import { useState } from 'react';
import { 
  Contain<PERSON>, 
  Typo<PERSON>, 
  <PERSON>, 
  But<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails,
  Chip,
  Alert
} from '@mui/material';
import { ExpandMore, PlayArrow, Assessment } from '@mui/icons-material';
import { LandingPageTester, TestResult } from '@/utils/testing';
import { DefaultBackground } from '@/components/shared/BackgroundElements';

export function TestingPanel() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [showReport, setShowReport] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setShowReport(false);
    
    try {
      const tester = new LandingPageTester();
      const results = await tester.runAllTests();
      setTestResults(results);
      setShowReport(true);
      
      // Log report to console
      console.log(tester.generateReport());
    } catch (error) {
      console.error('Error running tests:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass': return 'success';
      case 'warning': return 'warning';
      case 'fail': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass': return '✓';
      case 'warning': return '⚠';
      case 'fail': return '✗';
      default: return '?';
    }
  };

  const groupedResults = testResults.reduce((acc, result) => {
    if (!acc[result.category]) {
      acc[result.category] = [];
    }
    acc[result.category].push(result);
    return acc;
  }, {} as Record<string, TestResult[]>);

  const totalTests = testResults.length;
  const passedTests = testResults.filter(r => r.status === 'pass').length;
  const warningTests = testResults.filter(r => r.status === 'warning').length;
  const failedTests = testResults.filter(r => r.status === 'fail').length;

  return (
    <section className="relative min-h-screen py-20">
      <DefaultBackground />
      
      <Container maxWidth="lg" className="relative z-10">
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography 
            variant="h2" 
            className="text-gradient-white font-headline mb-4"
            sx={{ fontSize: { xs: '2.5rem', md: '3.5rem' } }}
          >
            Landing Page Testing Suite
          </Typography>
          <Typography 
            variant="h6" 
            className="text-gray-300 max-w-2xl mx-auto mb-6"
          >
            Comprehensive testing for responsive design, accessibility, SEO, and performance
          </Typography>
          
          <MuiButton
            variant="contained"
            size="large"
            startIcon={isRunning ? <Assessment /> : <PlayArrow />}
            onClick={runTests}
            disabled={isRunning}
            sx={{
              background: 'linear-gradient(45deg, #d4af37, #b8860b)',
              color: 'white',
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              '&:hover': {
                background: 'linear-gradient(45deg, #b8860b, #d4af37)',
              }
            }}
          >
            {isRunning ? 'Running Tests...' : 'Run All Tests'}
          </MuiButton>
        </Box>

        {showReport && (
          <Box sx={{ mb: 4 }}>
            <Alert 
              severity={failedTests > 0 ? 'error' : warningTests > 0 ? 'warning' : 'success'}
              sx={{ 
                mb: 3,
                backgroundColor: 'rgba(0, 0, 0, 0.3)',
                color: 'white',
                '& .MuiAlert-icon': {
                  color: failedTests > 0 ? '#f44336' : warningTests > 0 ? '#ff9800' : '#4caf50'
                }
              }}
            >
              <Typography variant="h6" sx={{ mb: 1 }}>
                Test Results Summary
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Chip 
                  label={`${passedTests} Passed`} 
                  color="success" 
                  variant="outlined"
                  sx={{ color: '#4caf50', borderColor: '#4caf50' }}
                />
                <Chip 
                  label={`${warningTests} Warnings`} 
                  color="warning" 
                  variant="outlined"
                  sx={{ color: '#ff9800', borderColor: '#ff9800' }}
                />
                <Chip 
                  label={`${failedTests} Failed`} 
                  color="error" 
                  variant="outlined"
                  sx={{ color: '#f44336', borderColor: '#f44336' }}
                />
                <Chip 
                  label={`${totalTests} Total`} 
                  variant="outlined"
                  sx={{ color: '#d4af37', borderColor: '#d4af37' }}
                />
              </Box>
            </Alert>

            {Object.entries(groupedResults).map(([category, results]) => (
              <Accordion 
                key={category}
                sx={{
                  backgroundColor: 'rgba(0, 0, 0, 0.3)',
                  color: 'white',
                  mb: 2,
                  '&:before': {
                    display: 'none',
                  },
                  '& .MuiAccordionSummary-root': {
                    backgroundColor: 'rgba(212, 175, 55, 0.1)',
                  }
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMore sx={{ color: '#d4af37' }} />}
                  aria-controls={`${category}-content`}
                  id={`${category}-header`}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                    <Typography variant="h6" className="text-amber-400">
                      {category}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, ml: 'auto' }}>
                      {results.map((result, index) => (
                        <Chip
                          key={index}
                          label={getStatusIcon(result.status)}
                          size="small"
                          color={getStatusColor(result.status)}
                          sx={{ minWidth: '32px', fontSize: '0.75rem' }}
                        />
                      ))}
                    </Box>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {results.map((result, index) => (
                      <Box 
                        key={index}
                        sx={{ 
                          display: 'flex', 
                          alignItems: 'flex-start', 
                          gap: 2,
                          p: 2,
                          backgroundColor: 'rgba(255, 255, 255, 0.05)',
                          borderRadius: 1,
                          borderLeft: `4px solid ${
                            result.status === 'pass' ? '#4caf50' : 
                            result.status === 'warning' ? '#ff9800' : '#f44336'
                          }`
                        }}
                      >
                        <Chip
                          label={getStatusIcon(result.status)}
                          size="small"
                          color={getStatusColor(result.status)}
                          sx={{ minWidth: '32px' }}
                        />
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="subtitle1" className="text-white font-medium">
                            {result.test}
                          </Typography>
                          <Typography variant="body2" className="text-gray-300">
                            {result.message}
                          </Typography>
                          {result.details && (
                            <Typography variant="caption" className="text-gray-400 mt-1 block">
                              {result.details}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </AccordionDetails>
              </Accordion>
            ))}
          </Box>
        )}

        {/* Testing Guidelines */}
        <Box sx={{ mt: 6 }}>
          <Typography variant="h4" className="text-amber-400 mb-4 text-center">
            Testing Guidelines
          </Typography>
          
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 4 }}>
            <Box className="glass-card-dark p-6 rounded-lg">
              <Typography variant="h6" className="text-amber-400 mb-3">
                Performance Targets
              </Typography>
              <ul className="text-gray-300 space-y-2">
                <li>• First Contentful Paint: &lt; 1.5s</li>
                <li>• Largest Contentful Paint: &lt; 2.5s</li>
                <li>• First Input Delay: &lt; 100ms</li>
                <li>• Cumulative Layout Shift: &lt; 0.1</li>
                <li>• DOM Content Loaded: &lt; 1.5s</li>
              </ul>
            </Box>
            
            <Box className="glass-card-dark p-6 rounded-lg">
              <Typography variant="h6" className="text-amber-400 mb-3">
                Accessibility Standards
              </Typography>
              <ul className="text-gray-300 space-y-2">
                <li>• WCAG 2.1 AA compliance</li>
                <li>• Keyboard navigation support</li>
                <li>• Screen reader compatibility</li>
                <li>• Proper heading hierarchy</li>
                <li>• Alt text for all images</li>
              </ul>
            </Box>
            
            <Box className="glass-card-dark p-6 rounded-lg">
              <Typography variant="h6" className="text-amber-400 mb-3">
                SEO Requirements
              </Typography>
              <ul className="text-gray-300 space-y-2">
                <li>• Unique page titles</li>
                <li>• Meta descriptions</li>
                <li>• Open Graph tags</li>
                <li>• Structured data markup</li>
                <li>• Semantic HTML structure</li>
              </ul>
            </Box>
            
            <Box className="glass-card-dark p-6 rounded-lg">
              <Typography variant="h6" className="text-amber-400 mb-3">
                Responsive Design
              </Typography>
              <ul className="text-gray-300 space-y-2">
                <li>• Mobile-first approach</li>
                <li>• Breakpoints: 480px, 768px, 1024px</li>
                <li>• Touch-friendly interactions</li>
                <li>• Readable text at all sizes</li>
                <li>• Optimized images</li>
              </ul>
            </Box>
          </Box>
        </Box>
      </Container>
    </section>
  );
}
