import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { MessageSquare } from "lucide-react";

export default function MessagesPage() {
  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="text-lg font-semibold md:text-2xl">Messages</h1>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Inbox</CardTitle>
          <CardDescription>
            Your real-time messaging interface will be available here.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center text-center p-12 border-2 border-dashed rounded-lg">
            <MessageSquare className="w-16 h-16 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold">Messaging Coming Soon</h3>
            <p className="text-muted-foreground mt-2">
              This feature is under construction. You'll soon be able to chat with freelancers in real-time.
            </p>
          </div>
        </Card<PERSON>ontent>
      </Card>
    </>
  );
}
