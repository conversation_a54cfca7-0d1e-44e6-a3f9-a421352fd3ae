import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

// Animation configurations
export const animationConfig = {
  duration: {
    fast: 0.3,
    normal: 0.6,
    slow: 1.2,
    verySlow: 2.0,
  },
  ease: {
    smooth: 'power2.out',
    bounce: 'back.out(1.7)',
    elastic: 'elastic.out(1, 0.3)',
    expo: 'expo.out',
    circ: 'circ.out',
  },
  stagger: {
    fast: 0.1,
    normal: 0.2,
    slow: 0.3,
  },
};

// Fade in animation
export const fadeIn = (element: string | Element, options: any = {}) => {
  const defaults = {
    duration: animationConfig.duration.normal,
    ease: animationConfig.ease.smooth,
    y: 30,
    opacity: 0,
  };
  
  return gsap.fromTo(
    element,
    { opacity: 0, y: defaults.y },
    { 
      opacity: 1, 
      y: 0, 
      duration: defaults.duration,
      ease: defaults.ease,
      ...options 
    }
  );
};

// Slide in from direction
export const slideIn = (element: string | Element, direction: 'left' | 'right' | 'up' | 'down' = 'up', options: any = {}) => {
  const directions = {
    left: { x: -100, y: 0 },
    right: { x: 100, y: 0 },
    up: { x: 0, y: 100 },
    down: { x: 0, y: -100 },
  };
  
  const { x, y } = directions[direction];
  
  return gsap.fromTo(
    element,
    { opacity: 0, x, y },
    {
      opacity: 1,
      x: 0,
      y: 0,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      ...options,
    }
  );
};

// Scale animation
export const scaleIn = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(
    element,
    { opacity: 0, scale: 0.8 },
    {
      opacity: 1,
      scale: 1,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.bounce,
      ...options,
    }
  );
};

// Stagger animation for multiple elements
export const staggerAnimation = (elements: string | Element[], options: any = {}) => {
  return gsap.fromTo(
    elements,
    { opacity: 0, y: 50 },
    {
      opacity: 1,
      y: 0,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      stagger: animationConfig.stagger.normal,
      ...options,
    }
  );
};

// Parallax effect
export const parallax = (element: string | Element, speed: number = 0.5) => {
  return gsap.to(element, {
    yPercent: -50 * speed,
    ease: 'none',
    scrollTrigger: {
      trigger: element,
      start: 'top bottom',
      end: 'bottom top',
      scrub: true,
    },
  });
};

// Scroll-triggered fade in
export const scrollFadeIn = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(
    element,
    { opacity: 0, y: 100 },
    {
      opacity: 1,
      y: 0,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      scrollTrigger: {
        trigger: element,
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse',
        ...options.scrollTrigger,
      },
      ...options,
    }
  );
};

// Hover animations
export const hoverLift = (element: string | Element) => {
  const el = typeof element === 'string' ? document.querySelector(element) : element;
  if (!el) return;

  el.addEventListener('mouseenter', () => {
    gsap.to(el, {
      y: -8,
      scale: 1.02,
      duration: animationConfig.duration.fast,
      ease: animationConfig.ease.smooth,
    });
  });

  el.addEventListener('mouseleave', () => {
    gsap.to(el, {
      y: 0,
      scale: 1,
      duration: animationConfig.duration.fast,
      ease: animationConfig.ease.smooth,
    });
  });
};

// Floating animation
export const float = (element: string | Element, options: any = {}) => {
  return gsap.to(element, {
    y: -20,
    duration: 2,
    ease: 'power1.inOut',
    yoyo: true,
    repeat: -1,
    ...options,
  });
};

// Rotate animation
export const rotate = (element: string | Element, options: any = {}) => {
  return gsap.to(element, {
    rotation: 360,
    duration: 10,
    ease: 'none',
    repeat: -1,
    ...options,
  });
};

// Text reveal animation
export const textReveal = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(
    element,
    { 
      opacity: 0,
      y: 100,
      skewY: 7,
    },
    {
      opacity: 1,
      y: 0,
      skewY: 0,
      duration: animationConfig.duration.slow,
      ease: animationConfig.ease.expo,
      ...options,
    }
  );
};

// Morphing background animation
export const morphBackground = (element: string | Element) => {
  return gsap.to(element, {
    backgroundPosition: '200% 0%',
    duration: 8,
    ease: 'none',
    repeat: -1,
    yoyo: true,
  });
};

// Loading animation
export const loadingPulse = (element: string | Element) => {
  return gsap.to(element, {
    opacity: 0.3,
    duration: 1,
    ease: 'power2.inOut',
    yoyo: true,
    repeat: -1,
  });
};

// Page transition
export const pageTransition = {
  enter: (element: string | Element) => {
    return gsap.fromTo(
      element,
      { opacity: 0, x: 100 },
      {
        opacity: 1,
        x: 0,
        duration: animationConfig.duration.normal,
        ease: animationConfig.ease.smooth,
      }
    );
  },
  exit: (element: string | Element) => {
    return gsap.to(element, {
      opacity: 0,
      x: -100,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
    });
  },
};

// Cleanup function for ScrollTrigger
export const cleanupScrollTriggers = () => {
  if (typeof window !== 'undefined') {
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }
};
