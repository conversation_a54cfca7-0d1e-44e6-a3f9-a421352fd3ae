import { Header } from '@/components/shared/Header';
import { Footer } from '@/components/shared/Footer';
import { Hero } from '@/components/landing/Hero';
import { Features } from '@/components/landing/Features';
import { Testimonials } from '@/components/landing/Testimonials';
import { InteractionsDemo } from '@/components/demo/InteractionsDemo';
import { TestingPanel } from '@/components/demo/TestingPanel';
import { HomePageSEO } from '@/components/shared/SEOHead';
import { SkipToMain } from '@/components/shared/AccessibilityUtils';
import { AccessibilityProvider } from '@/components/shared/AccessibilityProvider';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

function CallToAction() {
  return (
    <section className="bg-primary text-primary-foreground py-20 md:py-32">
      <div className="container mx-auto px-4 md:px-6 text-center">
        <h2 className="text-3xl md:text-4xl font-bold font-headline">Ready to Get Started?</h2>
        <p className="mt-4 max-w-2xl mx-auto text-lg text-primary-foreground/90">
          Join thousands of professionals who are building their businesses and finding their next big opportunity on VERTEX FORGE.
        </p>
        <div className="mt-8">
          <Button size="lg" variant="secondary" asChild>
            <Link href="/signup">Sign Up for Free</Link>
          </Button>
        </div>
      </div>
    </section>
  )
}

export default function Home() {
  return (
    <>
      <HomePageSEO />
      <AccessibilityProvider>
        <SkipToMain />
        <div className="min-h-screen bg-gradient-to-b from-black to-amber-900">
          <Header />
          <main id="main-content">
            <Hero />
            <Features />
            <Testimonials />
            <InteractionsDemo />
            <TestingPanel />
            <CallToAction />
          </main>
          <Footer />
        </div>
      </AccessibilityProvider>
    </>
  );
}
