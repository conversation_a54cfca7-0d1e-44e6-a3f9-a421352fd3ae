'use client';

import { useEffect, useRef, useCallback } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { 
  fadeIn, 
  slideIn, 
  scaleIn, 
  scrollFadeIn, 
  parallax, 
  hoverLift,
  float,
  rotate,
  textReveal,
  staggerAnimation,
  cleanupScrollTriggers
} from '@/utils/animations';

// Register plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

// Hook for basic GSAP animations
export const useGSAP = () => {
  const timelineRef = useRef<gsap.core.Timeline | null>(null);

  useEffect(() => {
    timelineRef.current = gsap.timeline();
    
    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
    };
  }, []);

  const animate = useCallback((animation: () => gsap.core.Tween | gsap.core.Timeline) => {
    if (timelineRef.current) {
      timelineRef.current.add(animation());
    }
  }, []);

  return { timeline: timelineRef.current, animate };
};

// Hook for scroll-triggered animations
export const useScrollAnimation = (trigger: string, options: any = {}) => {
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    const animation = scrollFadeIn(elementRef.current, options);

    return () => {
      animation.kill();
    };
  }, [options]);

  return elementRef;
};

// Hook for parallax effects
export const useParallax = (speed: number = 0.5) => {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    const animation = parallax(elementRef.current, speed);

    return () => {
      animation.kill();
    };
  }, [speed]);

  return elementRef;
};

// Hook for hover animations
export const useHoverAnimation = (animationType: 'lift' | 'scale' | 'rotate' = 'lift') => {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    const element = elementRef.current;

    switch (animationType) {
      case 'lift':
        hoverLift(element);
        break;
      case 'scale':
        element.addEventListener('mouseenter', () => {
          gsap.to(element, { scale: 1.05, duration: 0.3 });
        });
        element.addEventListener('mouseleave', () => {
          gsap.to(element, { scale: 1, duration: 0.3 });
        });
        break;
      case 'rotate':
        element.addEventListener('mouseenter', () => {
          gsap.to(element, { rotation: 5, duration: 0.3 });
        });
        element.addEventListener('mouseleave', () => {
          gsap.to(element, { rotation: 0, duration: 0.3 });
        });
        break;
    }
  }, [animationType]);

  return elementRef;
};

// Hook for entrance animations
export const useEntranceAnimation = (
  animationType: 'fadeIn' | 'slideIn' | 'scaleIn' | 'textReveal' = 'fadeIn',
  options: any = {}
) => {
  const elementRef = useRef<HTMLHeadingElement>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    let animation: gsap.core.Tween;

    switch (animationType) {
      case 'fadeIn':
        animation = fadeIn(elementRef.current, options);
        break;
      case 'slideIn':
        animation = slideIn(elementRef.current, options.direction || 'up', options);
        break;
      case 'scaleIn':
        animation = scaleIn(elementRef.current, options);
        break;
      case 'textReveal':
        animation = textReveal(elementRef.current, options);
        break;
      default:
        animation = fadeIn(elementRef.current, options);
    }

    return () => {
      animation.kill();
    };
  }, [animationType, options]);

  return elementRef;
};

// Hook for stagger animations
export const useStaggerAnimation = (options: any = {}) => {
  const containerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const children = containerRef.current.children;
    const animation = staggerAnimation(Array.from(children), options);

    return () => {
      animation.kill();
    };
  }, [options]);

  return containerRef;
};

// Hook for floating animations
export const useFloatingAnimation = (options: any = {}) => {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    const animation = float(elementRef.current, options);

    return () => {
      animation.kill();
    };
  }, [options]);

  return elementRef;
};

// Hook for rotation animations
export const useRotationAnimation = (options: any = {}) => {
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    const animation = rotate(elementRef.current, options);

    return () => {
      animation.kill();
    };
  }, [options]);

  return elementRef;
};

// Hook for page transitions
export const usePageTransition = () => {
  const containerRef = useRef<HTMLElement>(null);

  const enter = useCallback(() => {
    if (!containerRef.current) return;
    
    return gsap.fromTo(
      containerRef.current,
      { opacity: 0, x: 100 },
      { opacity: 1, x: 0, duration: 0.6, ease: 'power2.out' }
    );
  }, []);

  const exit = useCallback(() => {
    if (!containerRef.current) return;
    
    return gsap.to(containerRef.current, {
      opacity: 0,
      x: -100,
      duration: 0.6,
      ease: 'power2.out',
    });
  }, []);

  return { containerRef, enter, exit };
};

// Hook for cleanup on unmount
export const useGSAPCleanup = () => {
  useEffect(() => {
    return () => {
      cleanupScrollTriggers();
    };
  }, []);
};

// Hook for responsive animations
export const useResponsiveAnimation = (
  mobileAnimation: () => gsap.core.Tween,
  desktopAnimation: () => gsap.core.Tween
) => {
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!elementRef.current) return;

    const handleResize = () => {
      const isMobile = window.innerWidth < 768;
      
      if (isMobile) {
        mobileAnimation();
      } else {
        desktopAnimation();
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [mobileAnimation, desktopAnimation]);

  return elementRef;
};
