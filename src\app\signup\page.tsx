'use client';

import Link from "next/link";
import { Briefcase, Building, User } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { useState } from "react";
import { AadharVerification } from "@/components/auth/AadharVerification";

export default function SignupPage() {
    const [role, setRole] = useState('freelancer');

  return (
    <div className="flex items-center justify-center min-h-screen bg-muted/40 py-12">
      <div className="w-full max-w-lg p-4 md:p-0">
      <Link href="/" className="flex items-center justify-center gap-2 mb-8" prefetch={false}>
            <Briefcase className="h-8 w-8 text-primary" />
            <span className="text-3xl font-bold font-headline text-foreground">LancerLink</span>
          </Link>
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Create an account</CardTitle>
            <CardDescription>
              Join our community of clients and freelancers.
            </CardDescription>
          </CardHeader>
          <CardContent>
             <Tabs defaultValue="freelancer" className="w-full" onValueChange={(value) => setRole(value)}>
                <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="freelancer"><User className="mr-2 h-4 w-4" /> Join as a Freelancer</TabsTrigger>
                    <TabsTrigger value="client"><Building className="mr-2 h-4 w-4" /> Join as a Client</TabsTrigger>
                </TabsList>
                <TabsContent value="freelancer">
                   <div className="grid gap-4 mt-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="first-name">First name</Label>
                                <Input id="first-name" placeholder="Max" required />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="last-name">Last name</Label>
                                <Input id="last-name" placeholder="Robinson" required />
                            </div>
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="email">Email</Label>
                            <Input id="email" type="email" placeholder="<EMAIL>" required />
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="password">Password</Label>
                            <Input id="password" type="password" />
                        </div>
                        <AadharVerification />
                        <Button type="submit" className="w-full">
                            Create Freelancer Account
                        </Button>
                    </div>
                </TabsContent>
                <TabsContent value="client">
                    <div className="grid gap-4 mt-4">
                        <div className="grid gap-2">
                            <Label htmlFor="client-name">Full Name</Label>
                            <Input id="client-name" placeholder="Jane Doe" required />
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="client-email">Email</Label>
                            <Input id="client-email" type="email" placeholder="<EMAIL>" required />
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="client-password">Password</Label>
                            <Input id="client-password" type="password" />
                        </div>
                         <AadharVerification />
                        <Button type="submit" className="w-full">
                            Create Client Account
                        </Button>
                    </div>
                </TabsContent>
            </Tabs>

            <div className="mt-4 text-center text-sm">
              Already have an account?{" "}
              <Link href="/login" className="underline">
                Login
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
