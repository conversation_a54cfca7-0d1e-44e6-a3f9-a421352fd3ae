'use client';
import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import {
  Card as MuiCard,
  CardContent,
  Typography,
  Box,
  Container,
  Avatar,
  Rating,
  IconButton
} from '@mui/material';
import {
  FormatQuote,
  ArrowBackIos,
  ArrowForwardIos,
  Verified,
  WorkOutline,
  LocationOn
} from '@mui/icons-material';
import { useHoverAnimation } from '@/hooks/useGSAP';
import { TestimonialsBackground } from '@/components/shared/BackgroundElements';
import '@/styles/hero.css';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

const testimonials = [
  {
    id: 1,
    name: '<PERSON>.',
    role: 'Project Manager',
    company: 'TechCorp Inc.',
    location: 'San Francisco, CA',
    avatar: 'SL',
    image: 'https://placehold.co/100x100.png',
    testimonial: "VERTEX FORGE's verified freelancers and secure payment system gave me the confidence to hire for my biggest project yet. The quality of work was exceptional!",
    rating: 5,
    verified: true,
    delay: 0,
  },
  {
    id: 2,
    name: 'David C.',
    role: 'Full-Stack Developer',
    company: 'Freelancer',
    location: 'Austin, TX',
    avatar: 'DC',
    image: 'https://placehold.co/100x100.png',
    testimonial: 'As a freelancer, finding genuine clients is tough. VERTEX FORGE changed that. The AI matching is spot on and I landed a long-term client within a week.',
    rating: 5,
    verified: true,
    delay: 0.2,
  },
  {
    id: 3,
    name: 'Maria G.',
    role: 'Graphic Designer',
    company: 'Creative Studio',
    location: 'New York, NY',
    avatar: 'MG',
    image: 'https://placehold.co/100x100.png',
    testimonial: 'The platform is so intuitive. Building my portfolio was a breeze, and the collaboration tools made working with my client seamless. Highly recommended!',
    rating: 5,
    verified: true,
    delay: 0.4,
  },
  {
    id: 4,
    name: 'Alex R.',
    role: 'Marketing Director',
    company: 'Growth Labs',
    location: 'Los Angeles, CA',
    avatar: 'AR',
    image: 'https://placehold.co/100x100.png',
    testimonial: 'VERTEX FORGE has revolutionized how we find and work with freelancers. The quality of talent and the seamless workflow management is unmatched.',
    rating: 5,
    verified: true,
    delay: 0.6,
  },
  {
    id: 5,
    name: 'Jennifer K.',
    role: 'UX Designer',
    company: 'Design Co.',
    location: 'Seattle, WA',
    avatar: 'JK',
    image: 'https://placehold.co/100x100.png',
    testimonial: 'The collaboration tools and project management features make working with clients a breeze. I\'ve increased my productivity by 40% since joining.',
    rating: 5,
    verified: true,
    delay: 0.8,
  },
  {
    id: 6,
    name: 'Michael T.',
    role: 'Startup Founder',
    company: 'InnovateTech',
    location: 'Boston, MA',
    avatar: 'MT',
    image: 'https://placehold.co/100x100.png',
    testimonial: 'From concept to launch, VERTEX FORGE helped us find the perfect team. The talent pool is incredible and the platform makes collaboration effortless.',
    rating: 5,
    verified: true,
    delay: 1.0,
  },
];

// Testimonial card component with animations
function TestimonialCard({ testimonial }: { testimonial: any }) {
  const cardRef = useHoverAnimation('lift');
  const quoteRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!quoteRef.current) return;

    // Quote icon animation on hover
    const quoteElement = quoteRef.current;

    const handleMouseEnter = () => {
      gsap.to(quoteElement, {
        scale: 1.2,
        rotation: 10,
        duration: 0.3,
        ease: 'power2.out',
      });
    };

    const handleMouseLeave = () => {
      gsap.to(quoteElement, {
        scale: 1,
        rotation: 0,
        duration: 0.3,
        ease: 'power2.out',
      });
    };

    quoteElement.addEventListener('mouseenter', handleMouseEnter);
    quoteElement.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      quoteElement.removeEventListener('mouseenter', handleMouseEnter);
      quoteElement.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 60, scale: 0.9 }}
      whileInView={{
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
          duration: 0.6,
          delay: testimonial.delay,
          ease: [0.25, 0.46, 0.45, 0.94],
        }
      }}
      viewport={{ once: true, amount: 0.3 }}
      className="h-full"
    >
      <MuiCard
        ref={cardRef}
        sx={{
          height: '100%',
          background: 'linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%)',
          border: '1px solid #404040',
          borderRadius: '20px',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          position: 'relative',
          overflow: 'hidden',
          '&:hover': {
            borderColor: '#d4af37',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px #d4af3720',
            transform: 'translateY(-8px)',
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: 'linear-gradient(90deg, #d4af37, #b8860b)',
            opacity: 0,
            transition: 'opacity 0.3s ease',
          },
          '&:hover::before': {
            opacity: 1,
          },
        }}
      >
        <CardContent sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Quote icon */}
          <Box
            ref={quoteRef}
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              width: 40,
              height: 40,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #d4af3720, #d4af3710)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <FormatQuote sx={{ fontSize: 20, color: '#d4af37' }} />
          </Box>

          {/* Avatar and user info */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                mr: 2,
                background: 'linear-gradient(135deg, #d4af37, #b8860b)',
                fontSize: '1.5rem',
                fontWeight: 'bold',
              }}
            >
              {testimonial.avatar}
            </Avatar>
            <Box sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontFamily: 'Playfair Display, serif',
                    fontWeight: 600,
                    color: '#ffffff',
                  }}
                >
                  {testimonial.name}
                </Typography>
                {testimonial.verified && (
                  <Verified sx={{ fontSize: 16, color: '#d4af37' }} />
                )}
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <WorkOutline sx={{ fontSize: 14, color: '#888' }} />
                <Typography variant="body2" sx={{ color: '#888' }}>
                  {testimonial.role} at {testimonial.company}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocationOn sx={{ fontSize: 14, color: '#888' }} />
                <Typography variant="body2" sx={{ color: '#888' }}>
                  {testimonial.location}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Rating */}
          <Box sx={{ mb: 2 }}>
            <Rating
              value={testimonial.rating}
              readOnly
              sx={{
                '& .MuiRating-iconFilled': {
                  color: '#d4af37',
                },
                '& .MuiRating-iconEmpty': {
                  color: '#404040',
                },
              }}
            />
          </Box>

          {/* Testimonial text */}
          <Typography
            variant="body1"
            sx={{
              color: '#d4d4d4',
              lineHeight: 1.6,
              fontStyle: 'italic',
              flexGrow: 1,
              fontFamily: 'PT Sans, sans-serif',
              fontSize: '1rem',
            }}
          >
            "{testimonial.testimonial}"
          </Typography>
        </CardContent>
      </MuiCard>
    </motion.div>
  );
}

export function Testimonials() {
  const sectionRef = useRef<HTMLElement>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [visibleTestimonials, setVisibleTestimonials] = useState(3);

  useEffect(() => {
    const updateVisibleTestimonials = () => {
      if (window.innerWidth < 768) {
        setVisibleTestimonials(1);
      } else if (window.innerWidth < 1024) {
        setVisibleTestimonials(2);
      } else {
        setVisibleTestimonials(3);
      }
    };

    updateVisibleTestimonials();
    window.addEventListener('resize', updateVisibleTestimonials);
    return () => window.removeEventListener('resize', updateVisibleTestimonials);
  }, []);

  useEffect(() => {
    if (!sectionRef.current) return;

    // Background animation
    gsap.to('.testimonials-bg-shape', {
      rotation: 360,
      duration: 25,
      ease: 'none',
      repeat: -1,
    });

    // Parallax effect for background elements
    gsap.to('.testimonials-parallax', {
      yPercent: -20,
      ease: 'none',
      scrollTrigger: {
        trigger: sectionRef.current,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true,
      },
    });

    // Animate title and subtitle
    gsap.fromTo('.testimonials-title',
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        scrollTrigger: {
          trigger: '.testimonials-title',
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        }
      }
    );

    gsap.fromTo('.testimonials-subtitle',
      { opacity: 0, y: 30 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        delay: 0.3,
        scrollTrigger: {
          trigger: '.testimonials-subtitle',
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        }
      }
    );
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) =>
      prev + visibleTestimonials >= testimonials.length ? 0 : prev + visibleTestimonials
    );
  };

  const prevSlide = () => {
    setCurrentSlide((prev) =>
      prev === 0 ? Math.max(0, testimonials.length - visibleTestimonials) : Math.max(0, prev - visibleTestimonials)
    );
  };

  const visibleCards = testimonials.slice(currentSlide, currentSlide + visibleTestimonials);

  return (
    <section
      ref={sectionRef}
      id="testimonials"
      className="relative py-20 md:py-32 bg-gradient-to-b from-black via-gray-900 to-black overflow-hidden"
    >
      {/* Enhanced background elements */}
      <TestimonialsBackground />

      <Container maxWidth="xl" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Section header */}
        <Box textAlign="center" mb={8}>
          <Typography
            className="testimonials-title"
            variant="h2"
            sx={{
              fontFamily: 'Playfair Display, serif',
              fontWeight: 700,
              color: '#ffffff',
              mb: 3,
              background: 'linear-gradient(135deg, #ffffff 0%, #d4d4d4 50%, #ffffff 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              fontSize: { xs: '2.5rem', md: '3.5rem' },
            }}
          >
            Loved by Professionals Worldwide
          </Typography>
          <Typography
            className="testimonials-subtitle"
            variant="h6"
            sx={{
              color: '#d4d4d4',
              maxWidth: '600px',
              margin: '0 auto',
              lineHeight: 1.6,
              fontFamily: 'PT Sans, sans-serif',
              fontSize: { xs: '1.1rem', md: '1.25rem' },
            }}
          >
            Don't just take our word for it. Here's what our users have to say.
          </Typography>
        </Box>

        {/* Testimonials carousel */}
        <Box sx={{ position: 'relative', mt: 6 }}>
          {/* Navigation buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mb: 4 }}>
            <IconButton
              onClick={prevSlide}
              sx={{
                background: 'linear-gradient(135deg, #1a1a1a, #2a2a2a)',
                border: '1px solid #404040',
                color: '#d4af37',
                '&:hover': {
                  background: 'linear-gradient(135deg, #2a2a2a, #3a3a3a)',
                  borderColor: '#d4af37',
                },
              }}
            >
              <ArrowBackIos />
            </IconButton>
            <IconButton
              onClick={nextSlide}
              sx={{
                background: 'linear-gradient(135deg, #1a1a1a, #2a2a2a)',
                border: '1px solid #404040',
                color: '#d4af37',
                '&:hover': {
                  background: 'linear-gradient(135deg, #2a2a2a, #3a3a3a)',
                  borderColor: '#d4af37',
                },
              }}
            >
              <ArrowForwardIos />
            </IconButton>
          </Box>

          {/* Testimonials grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {visibleCards.map((testimonial) => (
              <TestimonialCard
                key={`${testimonial.id}-${currentSlide}`}
                testimonial={testimonial}
              />
            ))}
          </div>

          {/* Slide indicators */}
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mt: 4 }}>
            {Array.from({ length: Math.ceil(testimonials.length / visibleTestimonials) }).map((_, index) => (
              <Box
                key={index}
                onClick={() => setCurrentSlide(index * visibleTestimonials)}
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  background: currentSlide === index * visibleTestimonials ? '#d4af37' : '#404040',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: '#d4af37',
                    transform: 'scale(1.2)',
                  },
                }}
              />
            ))}
          </Box>
        </Box>
      </Container>
    </section>
  );
}
