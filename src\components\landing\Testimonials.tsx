'use client';
import { Card, CardContent } from '@/components/ui/card';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const testimonials = [
  {
    name: '<PERSON>, Project Manager',
    avatar: 'SL',
    image: 'https://placehold.co/100x100.png',
    testimonial: "LancerLink's verified freelancers and secure payment system gave me the confidence to hire for my biggest project yet. The quality of work was exceptional!",
  },
  {
    name: '<PERSON>, Full-Stack Developer',
    avatar: 'DC',
    image: 'https://placehold.co/100x100.png',
    testimonial: 'As a freelancer, finding genuine clients is tough. LancerLink changed that. The AI matching is spot on and I landed a long-term client within a week.',
  },
  {
    name: '<PERSON>, Graphic Designer',
    avatar: '<PERSON>',
    image: 'https://placehold.co/100x100.png',
    testimonial: 'The platform is so intuitive. Building my portfolio was a breeze, and the collaboration tools made working with my client seamless. Highly recommended!',
  },
];

export function Testimonials() {
  return (
    <section id="testimonials" className="py-20 md:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold">Loved by Professionals Worldwide</h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Don't just take our word for it. Here's what our users have to say.
          </p>
        </div>
        <Carousel
          opts={{
            align: 'start',
            loop: true,
          }}
          className="w-full max-w-4xl mx-auto mt-12"
        >
          <CarouselContent>
            {testimonials.map((item, index) => (
              <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3">
                <div className="p-1 h-full">
                  <Card className="flex flex-col justify-between h-full shadow-md">
                    <CardContent className="p-6 flex flex-col items-center text-center">
                      <Avatar className="w-20 h-20 mb-4 border-4 border-primary/20">
                        <AvatarImage src={item.image} alt={item.name} data-ai-hint="person" />
                        <AvatarFallback>{item.avatar}</AvatarFallback>
                      </Avatar>
                      <p className="text-muted-foreground italic">"{item.testimonial}"</p>
                      <p className="mt-4 font-bold font-headline text-primary">{item.name}</p>
                    </CardContent>
                  </Card>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      </div>
    </section>
  );
}
