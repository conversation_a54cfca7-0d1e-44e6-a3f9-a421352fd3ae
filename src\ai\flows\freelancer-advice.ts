'use server';

/**
 * @fileOverview An AI agent for providing freelancers with profile optimization tips and advice on finding genuine clients.
 *
 * - getFreelancerAdvice - A function that provides advice to freelancers on optimizing their profiles and finding genuine clients.
 * - FreelancerAdviceInput - The input type for the getFreelancerAdvice function.
 * - FreelancerAdviceOutput - The return type for the getFreelancerAdvice function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const FreelancerAdviceInputSchema = z.object({
  profileDetails: z
    .string()
    .describe('Details about the freelancer\'s current profile, including skills, experience, and portfolio information.'),
  desiredProjects: z
    .string()
    .describe('Description of the types of projects the freelancer is interested in.'),
});
export type FreelancerAdviceInput = z.infer<typeof FreelancerAdviceInputSchema>;

const FreelancerAdviceOutputSchema = z.object({
  profileTips: z
    .string()
    .describe('AI-powered tips on how to optimize the freelancer\'s profile to attract more clients.'),
  clientFindingAdvice: z
    .string()
    .describe('AI-powered advice on how to find genuine clients and navigate opportunities on the platform.'),
});
export type FreelancerAdviceOutput = z.infer<typeof FreelancerAdviceOutputSchema>;

export async function getFreelancerAdvice(input: FreelancerAdviceInput): Promise<FreelancerAdviceOutput> {
  return freelancerAdviceFlow(input);
}

const prompt = ai.definePrompt({
  name: 'freelancerAdvicePrompt',
  input: {schema: FreelancerAdviceInputSchema},
  output: {schema: FreelancerAdviceOutputSchema},
  prompt: `You are an AI-powered career coach specializing in advising freelancers on how to optimize their online profiles and find genuine clients on freelance marketplaces.

  Based on the freelancer's profile details and desired projects, provide actionable tips to improve their profile and strategies for finding suitable clients.

  Profile Details: {{{profileDetails}}}
  Desired Projects: {{{desiredProjects}}}

  Instructions:
  1.  Profile Tips: Provide specific, actionable tips on how the freelancer can improve their profile to attract more clients. This may include suggestions for improving their skills descriptions, portfolio presentation, or overall profile structure.
  2.  Client Finding Advice: Offer strategies for finding genuine clients and navigating opportunities on the platform. This may include advice on networking, using search filters, or identifying red flags.

  Format your response as follows:
  Profile Tips: [Profile optimization tips]
  Client Finding Advice: [Strategies for finding genuine clients] `,
});

const freelancerAdviceFlow = ai.defineFlow(
  {
    name: 'freelancerAdviceFlow',
    inputSchema: FreelancerAdviceInputSchema,
    outputSchema: FreelancerAdviceOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
