'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { UploadCloud, CheckCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '../ui/card';
import { Label } from '../ui/label';

type VerificationStatus = 'idle' | 'uploading' | 'verifying' | 'success' | 'error';

export function AadharVerification() {
  const [status, setStatus] = useState<VerificationStatus>('idle');
  const [progress, setProgress] = useState(0);

  const handleUpload = () => {
    setStatus('uploading');
    setProgress(0);
    const uploadInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(uploadInterval);
          setStatus('verifying');
          handleVerification();
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const handleVerification = () => {
    setTimeout(() => {
      // Simulate a 50/50 chance of success or error
      if (Math.random() > 0.2) {
        setStatus('success');
      } else {
        setStatus('error');
      }
    }, 2000);
  };

  const reset = () => {
    setStatus('idle');
    setProgress(0);
  };

  return (
    <Card className="bg-muted/30 border-dashed">
      <CardContent className="pt-6">
        <div className="grid gap-2 text-center">
            <Label htmlFor="aadhar" className="text-base font-semibold">Aadhar Verification</Label>
            <p className="text-sm text-muted-foreground">For security, please verify your identity. This helps keep LancerLink safe for everyone.</p>
        </div>
        <div className="mt-4 flex flex-col items-center justify-center gap-4">
          {status === 'idle' && (
            <Button onClick={handleUpload} variant="outline">
              <UploadCloud className="mr-2 h-4 w-4" />
              Upload Aadhar Card
            </Button>
          )}

          {(status === 'uploading' || status === 'verifying') && (
            <div className="w-full text-center">
              <Progress value={progress} className="w-full mb-2" />
              <p className="text-sm text-muted-foreground animate-pulse">
                {status === 'uploading' ? `Uploading... ${progress}%` : 'Verifying your details...'}
              </p>
            </div>
          )}

          {status === 'success' && (
            <div className="text-center text-green-600 flex flex-col items-center gap-2">
              <CheckCircle className="h-10 w-10" />
              <p className="font-semibold">Verification Successful!</p>
              <Button onClick={reset} variant="link" size="sm">Change Document</Button>
            </div>
          )}

          {status === 'error' && (
            <div className="text-center text-destructive flex flex-col items-center gap-2">
              <AlertCircle className="h-10 w-10" />
              <p className="font-semibold">Verification Failed</p>
              <p className="text-sm">Please try uploading a clearer image.</p>
              <Button onClick={handleUpload} variant="destructive" size="sm">Try Again</Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
