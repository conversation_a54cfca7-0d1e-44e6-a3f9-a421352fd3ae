'use client';
import { motion } from 'framer-motion';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useHoverAnimation } from '@/hooks/useGSAP';
import { Card as MuiCard, CardContent, Typography, Box, Container } from '@mui/material';
import {
  VerifiedUser,
  Payment,
  SmartToy,
  Chat,
  Speed,
  Security,
  TrendingUp,
  Support
} from '@mui/icons-material';
import { FeaturesBackground } from '@/components/shared/BackgroundElements';
import '@/styles/hero.css';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

const features = [
  {
    icon: VerifiedUser,
    title: 'Verified Professionals',
    description: 'All freelancers are verified via Aadhar for maximum trust and security.',
    color: '#8B4513',
    delay: 0.1,
  },
  {
    icon: Payment,
    title: 'Secure Payments',
    description: 'Escrow system ensures that payments are secure and released only upon project completion.',
    color: '#D2691E',
    delay: 0.2,
  },
  {
    icon: SmartToy,
    title: 'AI-Powered Matching',
    description: 'Our smart algorithm connects you with the perfect talent for your project needs.',
    color: '#CD853F',
    delay: 0.3,
  },
  {
    icon: Chat,
    title: 'Seamless Collaboration',
    description: 'Use our built-in tools to communicate, share files, and manage projects with ease.',
    color: '#A0522D',
    delay: 0.4,
  },
  {
    icon: Speed,
    title: 'Lightning Fast',
    description: 'Get matched with top talent in minutes, not days. Start your project immediately.',
    color: '#8B4513',
    delay: 0.5,
  },
  {
    icon: Security,
    title: 'Enterprise Security',
    description: 'Bank-level security protocols protect your data and intellectual property.',
    color: '#D2691E',
    delay: 0.6,
  },
  {
    icon: TrendingUp,
    title: 'Growth Analytics',
    description: 'Track project progress and team performance with detailed analytics and insights.',
    color: '#CD853F',
    delay: 0.7,
  },
  {
    icon: Support,
    title: '24/7 Support',
    description: 'Round-the-clock customer support to help you succeed at every step.',
    color: '#A0522D',
    delay: 0.8,
  },
];

// Feature card component with animations
function FeatureCard({ feature }: { feature: any }) {
  const cardRef = useHoverAnimation('lift');
  const iconRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!iconRef.current) return;

    // Icon hover animation
    const iconElement = iconRef.current;

    const handleMouseEnter = () => {
      gsap.to(iconElement, {
        scale: 1.1,
        rotation: 5,
        duration: 0.3,
        ease: 'power2.out',
      });
    };

    const handleMouseLeave = () => {
      gsap.to(iconElement, {
        scale: 1,
        rotation: 0,
        duration: 0.3,
        ease: 'power2.out',
      });
    };

    iconElement.addEventListener('mouseenter', handleMouseEnter);
    iconElement.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      iconElement.removeEventListener('mouseenter', handleMouseEnter);
      iconElement.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  const IconComponent = feature.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 60, scale: 0.9 }}
      whileInView={{
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
          duration: 0.6,
          delay: feature.delay,
          ease: [0.25, 0.46, 0.45, 0.94],
        }
      }}
      viewport={{ once: true, amount: 0.3 }}
      className="group"
    >
      <MuiCard
        ref={cardRef}
        sx={{
          height: '100%',
          background: 'linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%)',
          border: '1px solid #404040',
          borderRadius: '16px',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          position: 'relative',
          overflow: 'hidden',
          '&:hover': {
            borderColor: feature.color,
            boxShadow: `0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px ${feature.color}20`,
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: `linear-gradient(90deg, ${feature.color}, transparent)`,
            opacity: 0,
            transition: 'opacity 0.3s ease',
          },
          '&:hover::before': {
            opacity: 1,
          },
        }}
      >
        <CardContent sx={{ p: 4, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Icon container */}
          <Box
            ref={iconRef}
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              background: `linear-gradient(135deg, ${feature.color}20, ${feature.color}10)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px',
              position: 'relative',
              '&::before': {
                content: '""',
                position: 'absolute',
                inset: '-2px',
                borderRadius: '50%',
                background: `linear-gradient(135deg, ${feature.color}, transparent)`,
                opacity: 0,
                transition: 'opacity 0.3s ease',
                zIndex: -1,
              },
              '&:hover::before': {
                opacity: 0.3,
              },
            }}
          >
            <IconComponent
              sx={{
                fontSize: 40,
                color: feature.color,
                filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))',
              }}
            />
          </Box>

          {/* Title */}
          <Typography
            variant="h5"
            component="h3"
            sx={{
              fontFamily: 'Playfair Display, serif',
              fontWeight: 700,
              color: '#ffffff',
              mb: 2,
              background: `linear-gradient(135deg, #ffffff, ${feature.color}40)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            {feature.title}
          </Typography>

          {/* Description */}
          <Typography
            variant="body1"
            sx={{
              color: '#d4d4d4',
              lineHeight: 1.6,
              flexGrow: 1,
              fontFamily: 'PT Sans, sans-serif',
            }}
          >
            {feature.description}
          </Typography>
        </CardContent>
      </MuiCard>
    </motion.div>
  );
}

export function Features() {
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!sectionRef.current) return;

    // Background animation
    gsap.to('.features-bg-shape', {
      rotation: 360,
      duration: 20,
      ease: 'none',
      repeat: -1,
    });

    // Parallax effect for background elements
    gsap.to('.features-parallax', {
      yPercent: -30,
      ease: 'none',
      scrollTrigger: {
        trigger: sectionRef.current,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true,
      },
    });

    // Animate title and subtitle
    gsap.fromTo('.features-title',
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        scrollTrigger: {
          trigger: '.features-title',
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        }
      }
    );

    gsap.fromTo('.features-subtitle',
      { opacity: 0, y: 30 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        delay: 0.3,
        scrollTrigger: {
          trigger: '.features-subtitle',
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        }
      }
    );
  }, []);

  return (
    <section
      ref={sectionRef}
      id="features"
      className="relative py-20 md:py-32 bg-gradient-to-b from-black via-gray-900 to-black overflow-hidden"
    >
      {/* Enhanced background elements */}
      <FeaturesBackground />

      <Container maxWidth="xl" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Section header */}
        <Box textAlign="center" mb={8}>
          <Typography
            className="features-title"
            variant="h2"
            sx={{
              fontFamily: 'Playfair Display, serif',
              fontWeight: 700,
              color: '#ffffff',
              mb: 3,
              background: 'linear-gradient(135deg, #ffffff 0%, #d4d4d4 50%, #ffffff 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              fontSize: { xs: '2.5rem', md: '3.5rem' },
            }}
          >
            Why Choose VERTEX FORGE?
          </Typography>
          <Typography
            className="features-subtitle"
            variant="h6"
            sx={{
              color: '#d4d4d4',
              maxWidth: '600px',
              margin: '0 auto',
              lineHeight: 1.6,
              fontFamily: 'PT Sans, sans-serif',
              fontSize: { xs: '1.1rem', md: '1.25rem' },
            }}
          >
            A platform built on trust, efficiency, and cutting-edge technology to ensure your success.
          </Typography>
        </Box>

        {/* Features grid */}
        <Box sx={{ mt: 4 }}>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature) => (
              <FeatureCard key={feature.title} feature={feature} />
            ))}
          </div>
        </Box>
      </Container>
    </section>
  );
}
