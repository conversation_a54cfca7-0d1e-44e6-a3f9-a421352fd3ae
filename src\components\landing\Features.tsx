'use client';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from '@/components/ui/card';
import { Shield<PERSON>heck, Banknote, UserCheck, MessageSquare, Bot } from 'lucide-react';

const features = [
  {
    icon: <ShieldCheck className="w-10 h-10 text-primary" />,
    title: 'Verified Professionals',
    description: 'All freelancers are verified via Aadhar for maximum trust and security.',
  },
  {
    icon: <Banknote className="w-10 h-10 text-primary" />,
    title: 'Secure Payments',
    description: 'Escrow system ensures that payments are secure and released only upon project completion.',
  },
  {
    icon: <Bot className="w-10 h-10 text-primary" />,
    title: 'AI-Powered Matching',
    description: 'Our smart algorithm connects you with the perfect talent for your project needs.',
  },
  {
    icon: <MessageSquare className="w-10 h-10 text-primary" />,
    title: 'Seamless Collaboration',
    description: 'Use our built-in tools to communicate, share files, and manage projects with ease.',
  },
];

const cardVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

export function Features() {
  return (
    <section id="features" className="py-20 md:py-32 bg-muted/50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold">Why Choose VERTEX FORGE?</h2>
          <p className="mt-4 text-lg text-muted-foreground">
            A platform built on trust, efficiency, and cutting-edge technology to ensure your success.
          </p>
        </div>
        <div className="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              variants={cardVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.3 }}
              custom={index}
            >
              <Card className="h-full text-center hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="items-center">
                  <div className="bg-primary/10 p-4 rounded-full mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                  <CardDescription className="pt-2">{feature.description}</CardDescription>
                </CardHeader>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
